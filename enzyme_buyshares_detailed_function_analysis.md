# Enzyme Protocol - Детальный анализ функций в потоке buyShares

## 1. ComptrollerProxy.buyShares() - Точка входа

### 1.1 Полное описание функции

**Контракт:** ComptrollerLib.sol  
**Сигнатура:** `function buyShares(uint256 _investmentAmount, uint256 _minSharesQuantity) external override returns (uint256 sharesReceived_)`

**Назначение:** Основная точка входа для покупки долей фонда пользователем.

**Входные параметры:**
- `_investmentAmount` (uint256): Количество denomination asset для инвестирования
- `_minSharesQuantity` (uint256): Минимальное количество долей для получения (защита от slippage)

**Возвращаемые значения:**
- `sharesReceived_` (uint256): Фактическое количество полученных долей

### 1.2 Внутренняя логика

```solidity
function buyShares(uint256 _investmentAmount, uint256 _minSharesQuantity)
    external
    override
    returns (uint256 sharesReceived_)
{
    bool hasSharesActionTimelock = getSharesActionTimelock() > 0;
    address canonicalSender = __msgSender();

    return __buyShares(
        canonicalSender, _investmentAmount, _minSharesQuantity, hasSharesActionTimelock, canonicalSender
    );
}
```

**Пошаговое выполнение:**
1. Проверка наличия shares action timelock
2. Получение адреса отправителя через `__msgSender()`
3. Делегирование выполнения к `__buyShares()`

### 1.3 Взаимодействия

**Вызывает:**
- `getSharesActionTimelock()` - получение значения timelock
- `__msgSender()` - получение адреса отправителя
- `__buyShares()` - основная логика покупки долей

**События:** Нет (эмитируются в `__buyShares`)

**Изменения состояния:** Нет (происходят в `__buyShares`)

### 1.4 Критические аспекты безопасности

**Модификаторы доступа:**
- `external` - доступна для внешних вызовов
- `override` - переопределяет интерфейс

**Защита:** Все проверки безопасности делегированы к `__buyShares()`

---

## 2. __buyShares() - Основная логика

### 2.1 Полное описание функции

**Контракт:** ComptrollerLib.sol  
**Сигнатура:** `function __buyShares(address _buyer, uint256 _investmentAmount, uint256 _minSharesQuantity, bool _hasSharesActionTimelock, address _canonicalSender) private locksReentrance returns (uint256 sharesReceived_)`

**Назначение:** Выполняет всю логику покупки долей, включая валидацию, расчеты, переводы и выпуск долей.

**Входные параметры:**
- `_buyer` (address): Получатель долей
- `_investmentAmount` (uint256): Сумма инвестиций
- `_minSharesQuantity` (uint256): Минимальное количество долей
- `_hasSharesActionTimelock` (bool): Флаг наличия timelock
- `_canonicalSender` (address): Отправитель средств

### 2.2 Внутренняя логика

**Ключевые проверки:**
1. `require(_minSharesQuantity > 0)` - валидация минимального количества долей
2. `require(!_hasSharesActionTimelock || !__hasPendingMigrationOrReconfiguration())` - проверка состояния фонда

**Алгоритм выполнения:**
1. **Валидация параметров** - проверка входных данных
2. **Расчет GAV** - вызов `calcGav()`
3. **Pre-buy hooks** - выполнение логики комиссий
4. **Протокольные комиссии** - оплата и выкуп
5. **Перевод активов** - от пользователя к фонду
6. **Расчет долей** - определение количества к выпуску
7. **Выпуск долей** - mint в пользу покупателя
8. **Post-buy hooks** - финальная обработка комиссий и политик
9. **Валидация результата** - проверка slippage protection
10. **Обновление timelock** - если необходимо

### 2.3 Взаимодействия

**Вызывает:**
- `calcGav()` - расчет стоимости активов фонда
- `__preBuySharesHook()` - pre-buy обработка комиссий
- `IVault.payProtocolFee()` - оплата протокольной комиссии
- `__transferFromWithReceivedAmount()` - перевод активов
- `__calcGrossShareValue()` - расчет цены доли
- `IVault.mintShares()` - выпуск долей
- `__postBuySharesHook()` - post-buy обработка

**События:**
- `SharesBought(_buyer, receivedInvestmentAmount, sharesIssued, sharesReceived_)`

**Изменения состояния:**
- `acctToLastSharesBoughtTimestamp[_buyer] = block.timestamp` (если timelock активен)

### 2.4 Критические аспекты безопасности

**Модификаторы:**
- `private` - внутренняя функция
- `locksReentrance` - защита от реентрантности

**Проверки безопасности:**
- Валидация `_minSharesQuantity > 0`
- Проверка состояния миграции/реконфигурации
- Slippage protection через `sharesReceived_ >= _minSharesQuantity`

**Потенциальные векторы атак:**
- Price manipulation через GAV calculation
- Front-running атаки (защита через timelock)

---

## 3. calcGav() - Расчет стоимости активов

### 3.1 Полное описание функции

**Контракт:** ComptrollerLib.sol  
**Сигнатура:** `function calcGav() public override returns (uint256 gav_)`

**Назначение:** Вычисляет общую стоимость активов фонда (Gross Asset Value) в denomination asset.

### 3.2 Внутренняя логика

```solidity
function calcGav() public override returns (uint256 gav_) {
    address vaultProxyAddress = getVaultProxy();
    address[] memory assets = IVault(vaultProxyAddress).getTrackedAssets();
    address[] memory externalPositions = IVault(vaultProxyAddress).getActiveExternalPositions();

    if (assets.length == 0 && externalPositions.length == 0) {
        return 0;
    }

    // Расчет стоимости обычных активов
    uint256[] memory balances = new uint256[](assets.length);
    for (uint256 i; i < assets.length; i++) {
        balances[i] = IERC20(assets[i]).balanceOf(vaultProxyAddress);
    }

    gav_ = IValueInterpreter(getValueInterpreter()).calcCanonicalAssetsTotalValue(
        assets, balances, getDenominationAsset()
    );

    // Добавление стоимости external positions
    if (externalPositions.length > 0) {
        for (uint256 i; i < externalPositions.length; i++) {
            uint256 externalPositionValue = __calcExternalPositionValue(externalPositions[i]);
            gav_ += externalPositionValue;
        }
    }

    return gav_;
}
```

**Математические расчеты:**
- GAV = Σ(asset_value) + Σ(external_position_value)
- external_position_value = managed_assets_value - debt_assets_value

### 3.3 Взаимодействия

**Вызывает:**
- `IVault.getTrackedAssets()` - получение списка активов
- `IVault.getActiveExternalPositions()` - получение внешних позиций
- `IERC20.balanceOf()` - получение балансов активов
- `IValueInterpreter.calcCanonicalAssetsTotalValue()` - конвертация в denomination asset
- `__calcExternalPositionValue()` - расчет стоимости внешних позиций

**Внешние зависимости:**
- ValueInterpreter для ценообразования
- Chainlink price feeds (через ValueInterpreter)
- External position contracts

### 3.4 Критические аспекты безопасности

**Модификаторы:** `public override` - доступна для внешних вызовов

**Потенциальные риски:**
- Oracle manipulation attacks
- Stale price data usage
- External position valuation errors
- Gas limit issues при большом количестве активов

---

## 4. __preBuySharesHook() - Pre-Buy обработка

### 4.1 Полное описание функции

**Контракт:** ComptrollerLib.sol  
**Сигнатура:** `function __preBuySharesHook(address _buyer, uint256 _investmentAmount, uint256 _gav) private`

**Назначение:** Выполняет логику комиссий до выпуска долей.

### 4.2 Внутренняя логика

```solidity
function __preBuySharesHook(address _buyer, uint256 _investmentAmount, uint256 _gav) private {
    IFeeManager(getFeeManager()).invokeHook(
        IFeeManager.FeeHook.PreBuyShares, abi.encode(_buyer, _investmentAmount), _gav
    );
}
```

**Процесс:**
1. Кодирование данных: `abi.encode(_buyer, _investmentAmount)`
2. Вызов FeeManager с hook `PreBuyShares`
3. Передача GAV для расчета комиссий

### 4.3 Взаимодействия

**Вызывает:**
- `IFeeManager.invokeHook()` - обработка комиссий

**Изменения состояния:**
- Возможное взимание входных комиссий
- Обновление состояния fee contracts

### 4.4 Критические аспекты безопасности

**Модификаторы:** `private` - внутренняя функция

**Риски:**
- Malicious fee contracts
- Excessive gas consumption
- Fee calculation errors

---

## 5. FeeManager.invokeHook() - Обработка комиссий

### 5.1 Полное описание функции

**Контракт:** FeeManager.sol
**Сигнатура:** `function invokeHook(FeeHook _hook, bytes calldata _settlementData, uint256 _gav) external override`

**Назначение:** Координирует выполнение логики всех активных комиссий для определенного hook.

**Входные параметры:**
- `_hook` (FeeHook): Тип hook (PreBuyShares, PostBuyShares, etc.)
- `_settlementData` (bytes): Закодированные данные для расчета комиссий
- `_gav` (uint256): GAV фонда для расчетов

### 5.2 Внутренняя логика

```solidity
function invokeHook(FeeHook _hook, bytes calldata _settlementData, uint256 _gav) external override {
    __invokeHook(msg.sender, _hook, _settlementData, _gav, true);
}
```

**Делегирует к `__invokeHook()` который:**
1. Получает список активных комиссий для фонда
2. Выполняет `settle()` для каждой комиссии
3. Выполняет `update()` для каждой комиссии
4. Оптимизирует расчет GAV (вычисляет только при необходимости)

### 5.3 Взаимодействия

**Вызывает:**
- `getEnabledFeesForFund()` - получение списка активных комиссий
- `IFee.settlesOnHook()` - проверка необходимости settle
- `IFee.settle()` - выполнение логики комиссии
- `IFee.updatesOnHook()` - проверка необходимости update
- `IFee.update()` - обновление состояния комиссии

**Изменения состояния:**
- Возможное взимание комиссий (mint/burn shares)
- Обновление состояния fee contracts

### 5.4 Критические аспекты безопасности

**Модификаторы:** `external override` - вызывается только из ComptrollerProxy

**Защита:**
- Проверка активности фонда: `require(vaultProxy != address(0))`
- Контролируемый доступ через ComptrollerProxy

---

## 6. VaultProxy.payProtocolFee() - Протокольная комиссия

### 6.1 Полное описание функции

**Контракт:** VaultLib.sol
**Сигнатура:** `function payProtocolFee() external override onlyAccessor`

**Назначение:** Выплачивает накопленную протокольную комиссию путем выпуска долей в пользу ProtocolFeeReserve.

### 6.2 Внутренняя логика

```solidity
function payProtocolFee() external override onlyAccessor {
    uint256 sharesDue = IProtocolFeeTracker(getProtocolFeeTracker()).payFee();

    if (sharesDue == 0) {
        return;
    }

    __mint(getProtocolFeeReserve(), sharesDue);

    emit ProtocolFeePaidInShares(sharesDue);
}
```

**Процесс:**
1. Запрос к ProtocolFeeTracker для расчета комиссии
2. Если комиссия > 0, выпуск долей в пользу ProtocolFeeReserve
3. Эмиссия события

### 6.3 Взаимодействия

**Вызывает:**
- `IProtocolFeeTracker.payFee()` - расчет комиссии
- `__mint()` - выпуск долей

**События:**
- `ProtocolFeePaidInShares(sharesDue)`

**Изменения состояния:**
- Увеличение totalSupply долей
- Увеличение баланса ProtocolFeeReserve

### 6.4 Критические аспекты безопасности

**Модификаторы:**
- `external override` - внешняя функция
- `onlyAccessor` - только ComptrollerProxy может вызывать

---

## 7. __transferFromWithReceivedAmount() - Перевод активов

### 7.1 Полное описание функции

**Контракт:** ComptrollerLib.sol
**Сигнатура:** `function __transferFromWithReceivedAmount(address _asset, address _sender, address _recipient, uint256 _transferAmount) private returns (uint256 receivedAmount_)`

**Назначение:** Выполняет перевод ERC20 токенов с точным расчетом полученной суммы (учитывает transfer fees).

### 7.2 Внутренняя логика

```solidity
function __transferFromWithReceivedAmount(
    address _asset,
    address _sender,
    address _recipient,
    uint256 _transferAmount
) private returns (uint256 receivedAmount_) {
    uint256 preTransferRecipientBalance = IERC20(_asset).balanceOf(_recipient);

    IERC20(_asset).safeTransferFrom(_sender, _recipient, _transferAmount);

    return IERC20(_asset).balanceOf(_recipient) - preTransferRecipientBalance;
}
```

**Алгоритм:**
1. Сохранение баланса получателя до перевода
2. Выполнение `safeTransferFrom()`
3. Расчет фактически полученной суммы

**Математика:** `receivedAmount = balanceAfter - balanceBefore`

### 7.3 Взаимодействия

**Вызывает:**
- `IERC20.balanceOf()` - получение балансов
- `IERC20.safeTransferFrom()` - безопасный перевод

**Изменения состояния:**
- Уменьшение баланса отправителя
- Увеличение баланса получателя (vault)

### 7.4 Критические аспекты безопасности

**Модификаторы:** `private` - внутренняя функция

**Защита:**
- Использование `safeTransferFrom()` для защиты от revert
- Точный расчет полученной суммы для токенов с transfer fees

**Риски:**
- Reentrancy через malicious ERC20 (защищено модификатором в `__buyShares`)
- Deflationary tokens могут вызвать неожиданное поведение

---

## 8. __calcGrossShareValue() - Расчет цены доли

### 8.1 Полное описание функции

**Контракт:** ComptrollerLib.sol
**Сигнатура:** `function __calcGrossShareValue(uint256 _gav, uint256 _sharesSupply, uint256 _denominationAssetUnit) private pure returns (uint256 grossShareValue_)`

**Назначение:** Вычисляет цену одной доли фонда в denomination asset.

### 8.2 Внутренняя логика

```solidity
function __calcGrossShareValue(uint256 _gav, uint256 _sharesSupply, uint256 _denominationAssetUnit)
    private
    pure
    returns (uint256 grossShareValue_)
{
    if (_sharesSupply == 0) {
        return _denominationAssetUnit;
    }

    return _gav * SHARES_UNIT / _sharesSupply;
}
```

**Математические формулы:**
- Если `totalSupply == 0`: `sharePrice = denominationAssetUnit` (обычно 10^18)
- Иначе: `sharePrice = (GAV * SHARES_UNIT) / totalSupply`
- Где `SHARES_UNIT = 10^18`

### 8.3 Взаимодействия

**Вызывает:** Нет (pure function)

**Изменения состояния:** Нет

### 8.4 Критические аспекты безопасности

**Модификаторы:** `private pure` - чистая функция без побочных эффектов

**Математические риски:**
- Division by zero (защищено проверкой `_sharesSupply == 0`)
- Integer overflow (защищено Solidity 0.8+)
- Precision loss при делении
