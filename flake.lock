{"nodes": {"flake-utils": {"locked": {"lastModified": 1644229661, "narHash": "sha256-1YdnJAsNy69bpcjuoKdOYQX0YxZBiCYZo4Twxerqv7k=", "owner": "numtide", "repo": "flake-utils", "rev": "3cecb5b042f7f209c56ffd8371b2711a290ec797", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "foundry": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1726477824, "narHash": "sha256-9JY2GG6IqEtl5N5xJkct+oTDE3e3l5jlhZ3rIpIdzHo=", "owner": "shazow", "repo": "foundry.nix", "rev": "c46196f8eb593455757f666077c5ad3aa72db99d", "type": "github"}, "original": {"owner": "shazow", "repo": "foundry.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1666753130, "narHash": "sha256-Wff1dGPFSneXJLI2c0kkdWTgxnQ416KE6X4KnFkgPYQ=", "owner": "NixOS", "repo": "nixpkgs", "rev": "f540aeda6f677354f1e7144ab04352f61aaa0118", "type": "github"}, "original": {"id": "nixpkgs", "type": "indirect"}}, "nixpkgs_2": {"locked": {"lastModified": 1726481836, "narHash": "sha256-MWTBH4dd5zIz2iatDb8IkqSjIeFum9jAqkFxgHLdzO4=", "owner": "nixos", "repo": "nixpkgs", "rev": "20f9370d5f588fb8c72e844c54511cab054b5f40", "type": "github"}, "original": {"owner": "nixos", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"foundry": "foundry", "nixpkgs": "nixpkgs_2", "utils": "utils"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1710146030, "narHash": "sha256-SZ5L6eA7HJ/nmkzGG7/ISclqe6oZdOZTNoesiInkXPQ=", "owner": "numtide", "repo": "flake-utils", "rev": "b1d9ab70662946ef0850d488da1c9019f3a9752a", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}}, "root": "root", "version": 7}