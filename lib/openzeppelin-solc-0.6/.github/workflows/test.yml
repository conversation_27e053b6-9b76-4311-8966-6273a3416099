name: Test

on:
  push:
    branches:
      - master
      - release-v*
  pull_request: {}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 10.x
      - uses: actions/cache@v2
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run lint
      - run: npm run test

  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 10.x
      - uses: actions/cache@v2
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run coverage
      - uses: codecov/codecov-action@v1
