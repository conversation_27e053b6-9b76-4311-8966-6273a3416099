name: Foundry

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

jobs:
  forge-test:
    strategy:
      fail-fast: true
      matrix:
        type: ["slow", "fast"]
        include:
          - type: "slow"
            fuzz-runs: 10000
            max-test-rejects: 500000
            invariant-runs: 0
            invariant-depth: 512
          - type: "fast"
            fuzz-runs: 256
            max-test-rejects: 65536
            invariant-runs: 0
            invariant-depth: 256

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Run Forge tests in ${{ matrix.type }} mode
        run: yarn test:forge -vvv
        env:
          FOUNDRY_FUZZ_RUNS: ${{ matrix.fuzz-runs }}
          FOUNDRY_FUZZ_MAX_TEST_REJECTS: ${{ matrix.max-test-rejects }}
          FOUNDRY_INVARIANT_RUNS: ${{ matrix.invariant-runs }}
          FOUNDRY_INVARIANT_DEPTH: ${{ matrix.invariant-depth }}
          FOUNDRY_FUZZ_SEED: 0x${{ github.event.pull_request.base.sha || github.sha }}
