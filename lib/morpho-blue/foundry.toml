[profile.default]
names = true
sizes = true
via-ir = true
optimizer_runs = 999999 # Etherscan does not support verifying contracts with more optimization runs.

[profile.default.invariant]
runs = 8
depth = 256
fail_on_revert = true

[profile.default.fmt]
wrap_comments = true


[profile.build]
test = "/dev/null"
script = "/dev/null"


[profile.test]
via-ir = false


# See more config options https://github.com/foundry-rs/foundry/tree/master/crates/config
