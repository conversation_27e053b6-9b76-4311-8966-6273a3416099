// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NFTDescriptor #constructTokenURI gas 1`] = `1632898`;

exports[`NFTDescriptor #constructTokenURI snapshot matches 1`] = `"data:application/json;base64,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"`;

exports[`NFTDescriptor #svgImage matches the current snapshot 1`] = `"<svg width=\\"290\\" height=\\"500\\" viewBox=\\"0 0 290 500\\" xmlns=\\"http://www.w3.org/2000/svg\\" xmlns:xlink='http://www.w3.org/1999/xlink'><defs><filter id=\\"f1\\"><feImage result=\\"p0\\" xlink:href=\\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjkwJyBoZWlnaHQ9JzUwMCcgdmlld0JveD0nMCAwIDI5MCA1MDAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHJlY3Qgd2lkdGg9JzI5MHB4JyBoZWlnaHQ9JzUwMHB4JyBmaWxsPScjMTIzNDU2Jy8+PC9zdmc+\\"/><feImage result=\\"p1\\" xlink:href=\\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjkwJyBoZWlnaHQ9JzUwMCcgdmlld0JveD0nMCAwIDI5MCA1MDAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PGNpcmNsZSBjeD0nMTkyJyBjeT0nMzI1JyByPScxMjBweCcgZmlsbD0nI2FiY2RlYScvPjwvc3ZnPg==\\"/><feImage result=\\"p2\\" xlink:href=\\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjkwJyBoZWlnaHQ9JzUwMCcgdmlld0JveD0nMCAwIDI5MCA1MDAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PGNpcmNsZSBjeD0nMjQzJyBjeT0nMzYyJyByPScxMjBweCcgZmlsbD0nIzY3ODkwMScvPjwvc3ZnPg==\\" /><feImage result=\\"p3\\" xlink:href=\\"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjkwJyBoZWlnaHQ9JzUwMCcgdmlld0JveD0nMCAwIDI5MCA1MDAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PGNpcmNsZSBjeD0nMzcnIGN5PScxMzEnIHI9JzEwMHB4JyBmaWxsPScjZmFiY2RmJy8+PC9zdmc+\\" /><feBlend mode=\\"overlay\\" in=\\"p0\\" in2=\\"p1\\" /><feBlend mode=\\"exclusion\\" in2=\\"p2\\" /><feBlend mode=\\"overlay\\" in2=\\"p3\\" result=\\"blendOut\\" /><feGaussianBlur in=\\"blendOut\\" stdDeviation=\\"42\\" /></filter> <clipPath id=\\"corners\\"><rect width=\\"290\\" height=\\"500\\" rx=\\"42\\" ry=\\"42\\" /></clipPath><path id=\\"text-path-a\\" d=\\"M40 12 H250 A28 28 0 0 1 278 40 V460 A28 28 0 0 1 250 488 H40 A28 28 0 0 1 12 460 V40 A28 28 0 0 1 40 12 z\\" /><path id=\\"minimap\\" d=\\"M234 444C234 457.949 242.21 463 253 463\\" /><filter id=\\"top-region-blur\\"><feGaussianBlur in=\\"SourceGraphic\\" stdDeviation=\\"24\\" /></filter><linearGradient id=\\"grad-up\\" x1=\\"1\\" x2=\\"0\\" y1=\\"1\\" y2=\\"0\\"><stop offset=\\"0.0\\" stop-color=\\"white\\" stop-opacity=\\"1\\" /><stop offset=\\".9\\" stop-color=\\"white\\" stop-opacity=\\"0\\" /></linearGradient><linearGradient id=\\"grad-down\\" x1=\\"0\\" x2=\\"1\\" y1=\\"0\\" y2=\\"1\\"><stop offset=\\"0.0\\" stop-color=\\"white\\" stop-opacity=\\"1\\" /><stop offset=\\"0.9\\" stop-color=\\"white\\" stop-opacity=\\"0\\" /></linearGradient><mask id=\\"fade-up\\" maskContentUnits=\\"objectBoundingBox\\"><rect width=\\"1\\" height=\\"1\\" fill=\\"url(#grad-up)\\" /></mask><mask id=\\"fade-down\\" maskContentUnits=\\"objectBoundingBox\\"><rect width=\\"1\\" height=\\"1\\" fill=\\"url(#grad-down)\\" /></mask><mask id=\\"none\\" maskContentUnits=\\"objectBoundingBox\\"><rect width=\\"1\\" height=\\"1\\" fill=\\"white\\" /></mask><linearGradient id=\\"grad-symbol\\"><stop offset=\\"0.7\\" stop-color=\\"white\\" stop-opacity=\\"1\\" /><stop offset=\\".95\\" stop-color=\\"white\\" stop-opacity=\\"0\\" /></linearGradient><mask id=\\"fade-symbol\\" maskContentUnits=\\"userSpaceOnUse\\"><rect width=\\"290px\\" height=\\"200px\\" fill=\\"url(#grad-symbol)\\" /></mask></defs><g clip-path=\\"url(#corners)\\"><rect fill=\\"123456\\" x=\\"0px\\" y=\\"0px\\" width=\\"290px\\" height=\\"500px\\" /><rect style=\\"filter: url(#f1)\\" x=\\"0px\\" y=\\"0px\\" width=\\"290px\\" height=\\"500px\\" /> <g style=\\"filter:url(#top-region-blur); transform:scale(1.5); transform-origin:center top;\\"><rect fill=\\"none\\" x=\\"0px\\" y=\\"0px\\" width=\\"290px\\" height=\\"500px\\" /><ellipse cx=\\"50%\\" cy=\\"0px\\" rx=\\"180px\\" ry=\\"120px\\" fill=\\"#000\\" opacity=\\"0.85\\" /></g><rect x=\\"0\\" y=\\"0\\" width=\\"290\\" height=\\"500\\" rx=\\"42\\" ry=\\"42\\" fill=\\"rgba(0,0,0,0)\\" stroke=\\"rgba(255,255,255,0.2)\\" /></g><text text-rendering=\\"optimizeSpeed\\"><textPath startOffset=\\"-100%\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-size=\\"10px\\" xlink:href=\\"#text-path-a\\">****************************************** • WETH <animate additive=\\"sum\\" attributeName=\\"startOffset\\" from=\\"0%\\" to=\\"100%\\" begin=\\"0s\\" dur=\\"30s\\" repeatCount=\\"indefinite\\" /></textPath> <textPath startOffset=\\"0%\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-size=\\"10px\\" xlink:href=\\"#text-path-a\\">****************************************** • WETH <animate additive=\\"sum\\" attributeName=\\"startOffset\\" from=\\"0%\\" to=\\"100%\\" begin=\\"0s\\" dur=\\"30s\\" repeatCount=\\"indefinite\\" /> </textPath><textPath startOffset=\\"50%\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-size=\\"10px\\" xlink:href=\\"#text-path-a\\">****************************************** • UNI <animate additive=\\"sum\\" attributeName=\\"startOffset\\" from=\\"0%\\" to=\\"100%\\" begin=\\"0s\\" dur=\\"30s\\" repeatCount=\\"indefinite\\" /></textPath><textPath startOffset=\\"-50%\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-size=\\"10px\\" xlink:href=\\"#text-path-a\\">****************************************** • UNI <animate additive=\\"sum\\" attributeName=\\"startOffset\\" from=\\"0%\\" to=\\"100%\\" begin=\\"0s\\" dur=\\"30s\\" repeatCount=\\"indefinite\\" /></textPath></text><g mask=\\"url(#fade-symbol)\\"><rect fill=\\"none\\" x=\\"0px\\" y=\\"0px\\" width=\\"290px\\" height=\\"200px\\" /> <text y=\\"70px\\" x=\\"32px\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-weight=\\"200\\" font-size=\\"36px\\">UNI/WETH</text><text y=\\"115px\\" x=\\"32px\\" fill=\\"white\\" font-family=\\"'Courier New', monospace\\" font-weight=\\"200\\" font-size=\\"36px\\">0.05%</text></g><rect x=\\"16\\" y=\\"16\\" width=\\"258\\" height=\\"468\\" rx=\\"26\\" ry=\\"26\\" fill=\\"rgba(0,0,0,0)\\" stroke=\\"rgba(255,255,255,0.2)\\" /><g mask=\\"url(#none)\\" style=\\"transform:translate(72px,189px)\\"><rect x=\\"-16px\\" y=\\"-16px\\" width=\\"180px\\" height=\\"180px\\" fill=\\"none\\" /><path d=\\"M1 1C17 73 73 129 145 145\\" stroke=\\"rgba(0,0,0,0.3)\\" stroke-width=\\"32px\\" fill=\\"none\\" stroke-linecap=\\"round\\" /></g><g mask=\\"url(#none)\\" style=\\"transform:translate(72px,189px)\\"><rect x=\\"-16px\\" y=\\"-16px\\" width=\\"180px\\" height=\\"180px\\" fill=\\"none\\" /><path d=\\"M1 1C17 73 73 129 145 145\\" stroke=\\"rgba(255,255,255,1)\\" fill=\\"none\\" stroke-linecap=\\"round\\" /></g><circle cx=\\"73px\\" cy=\\"190px\\" r=\\"4px\\" fill=\\"white\\" /><circle cx=\\"217px\\" cy=\\"334px\\" r=\\"4px\\" fill=\\"white\\" /> <g style=\\"transform:translate(29px, 384px)\\"><rect width=\\"77px\\" height=\\"26px\\" rx=\\"8px\\" ry=\\"8px\\" fill=\\"rgba(0,0,0,0.6)\\" /><text x=\\"12px\\" y=\\"17px\\" font-family=\\"'Courier New', monospace\\" font-size=\\"12px\\" fill=\\"white\\"><tspan fill=\\"rgba(255,255,255,0.6)\\">ID: </tspan>123</text></g> <g style=\\"transform:translate(29px, 414px)\\"><rect width=\\"133px\\" height=\\"26px\\" rx=\\"8px\\" ry=\\"8px\\" fill=\\"rgba(0,0,0,0.6)\\" /><text x=\\"12px\\" y=\\"17px\\" font-family=\\"'Courier New', monospace\\" font-size=\\"12px\\" fill=\\"white\\"><tspan fill=\\"rgba(255,255,255,0.6)\\">Min Tick: </tspan>-1000</text></g> <g style=\\"transform:translate(29px, 444px)\\"><rect width=\\"126px\\" height=\\"26px\\" rx=\\"8px\\" ry=\\"8px\\" fill=\\"rgba(0,0,0,0.6)\\" /><text x=\\"12px\\" y=\\"17px\\" font-family=\\"'Courier New', monospace\\" font-size=\\"12px\\" fill=\\"white\\"><tspan fill=\\"rgba(255,255,255,0.6)\\">Max Tick: </tspan>2000</text></g><g style=\\"transform:translate(226px, 433px)\\"><rect width=\\"36px\\" height=\\"36px\\" rx=\\"8px\\" ry=\\"8px\\" fill=\\"none\\" stroke=\\"rgba(255,255,255,0.2)\\" /><path stroke-linecap=\\"round\\" d=\\"M8 9C8.00004 22.9494 16.2099 28 27 28\\" fill=\\"none\\" stroke=\\"white\\" /><circle style=\\"transform:translate3d(13px, 23px, 0px)\\" cx=\\"0px\\" cy=\\"0px\\" r=\\"4px\\" fill=\\"white\\"/></g></svg>"`;
