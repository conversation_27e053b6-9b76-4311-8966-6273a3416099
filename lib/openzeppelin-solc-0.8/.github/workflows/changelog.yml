name: changelog

on:
  pull_request:
    types:
      - opened
      - synchronize
      - labeled
      - unlabeled

concurrency:
  group: changelog-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.pull_request.labels.*.name, 'ignore-changelog') }}
    steps:
      - uses: actions/checkout@v3
      - name: Check diff
        run: |
          git fetch origin ${{ github.base_ref }} --depth=1
          if git diff --exit-code origin/${{ github.base_ref }} -- CHANGELOG.md ; then
            echo 'Missing changelog entry'
            exit 1
          fi

