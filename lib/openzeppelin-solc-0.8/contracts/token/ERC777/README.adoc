= ERC 777

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/token/erc777

This set of interfaces and contracts are all related to the https://eips.ethereum.org/EIPS/eip-777[ERC777 token standard].

TIP: For an overview of ERC777 tokens and a walk through on how to create a token contract read our xref:ROOT:erc777.adoc[ERC777 guide].

The token behavior itself is implemented in the core contracts: {IERC777}, {ERC777}.

Additionally there are interfaces used to develop contracts that react to token movements: {IERC777Sender}, {IERC777Recipient}.

== Core

{{IERC777}}

{{ERC777}}

== Hooks

{{IERC777Sender}}

{{IERC777Recipient}}

== Presets

These contracts are preconfigured combinations of features. They can be used through inheritance or as models to copy and paste their source code.

{{ERC777PresetFixedSupply}}
