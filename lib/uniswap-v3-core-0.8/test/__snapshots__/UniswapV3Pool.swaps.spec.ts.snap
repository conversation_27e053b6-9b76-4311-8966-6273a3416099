// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`UniswapV3Pool swap tests close to max price swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "1000",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-26083549850867114346332688477747755628",
  "executionPrice": "2.6084e+34",
  "feeGrowthGlobal0X128Delta": "2381976568446569244235",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.1734e+30",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 705098,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "0",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "1000",
  "executionPrice": "-Infinity",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "1.7014e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 880340,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-26087635650665564420687107504180041533",
  "executionPrice": "2.6088e+19",
  "feeGrowthGlobal0X128Delta": "510423550381413479995299567101531162",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.0241",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 13923,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-26087635650665564420687107504180041533",
  "executionPrice": "2.6088e+19",
  "feeGrowthGlobal0X128Delta": "510423550381413479995299567101531162",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.0241",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 13923,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "0",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "-Infinity",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195061911147652317",
  "poolPriceAfter": "1.7014e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 880340,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "1",
  "poolBalance1": "26087635650665564424699143612505016738",
  "poolPriceBefore": "1.7014e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "2",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-1000",
  "executionPrice": "500.00",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.7014e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 880340,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "2",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "5.0000e+17",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.7014e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 880340,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "2",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "5.0000e+17",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.7014e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 880340,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "3171793039286238109",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-26087635650665564423434232548437664977",
  "executionPrice": "8.2249e+18",
  "feeGrowthGlobal0X128Delta": "1618957864187523123655042148763283097",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": -9164,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token0 for token1 to price 2.5000 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "1268717215714495281",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "-26087635650665564421536865952336637378",
  "executionPrice": "2.0562e+19",
  "feeGrowthGlobal0X128Delta": "647583145675012618257449376796101507",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 9163,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "0",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "10740898373457544742072477595619363803",
  "executionPrice": "-Infinity",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "5482407482066087054477299856254072312542046383926535301",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 887271,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "1",
  "amount0Delta": "0",
  "amount1Before": "26087635650665564424699143612505016738",
  "amount1Delta": "10740898373457544742072477595619363803",
  "executionPrice": "-Infinity",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "5482407482066087054477299856254072312542046383926535301",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.7014e+38",
  "tickAfter": 887271,
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "1",
  "poolBalance1": "26087635650665564424699143612505016738",
  "poolPriceBefore": "1.7014e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "1",
  "poolBalance1": "26087635650665564424699143612505016738",
  "poolPriceBefore": "1.7014e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to max price swap token1 for token0 to price 2.5000 1`] = `
Object {
  "poolBalance0": "1",
  "poolBalance1": "26087635650665564424699143612505016738",
  "poolPriceBefore": "1.7014e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 880340,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "1000",
  "amount1Before": "1",
  "amount1Delta": "0",
  "executionPrice": "0.0000",
  "feeGrowthGlobal0X128Delta": "170141183460469231731687",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000059000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -880303,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-26033697540846965126433148994127431276",
  "amount1Before": "1",
  "amount1Delta": "1000",
  "executionPrice": "0.000000000000000000000000000000000038412",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "2381976568446569244235",
  "poolPriceAfter": "0.00000000000000000000000000000023974",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -705093,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "1",
  "amount1Delta": "0",
  "executionPrice": "0.0000",
  "feeGrowthGlobal0X128Delta": "170141183460469231731687303715884105728",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000059000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -880303,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "26037782196502120275425782622539039026",
  "poolBalance1": "1",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-26037782196502120271413746514214063808",
  "amount1Before": "1",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.000000000000000000038406",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381413820277666488039994629",
  "poolPriceAfter": "0.24850",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -13924,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-26037782196502120271413746514214063808",
  "amount1Before": "1",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.000000000000000000038406",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381413820277666488039994629",
  "poolPriceAfter": "0.24850",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -13924,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "10790901831095468191587263901270792610",
  "amount1Before": "1",
  "amount1Delta": "0",
  "executionPrice": "0.0000",
  "feeGrowthGlobal0X128Delta": "5507930424444982259736347157352787128931407551935325049",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -887272,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "10790901831095468191587263901270792610",
  "amount1Before": "1",
  "amount1Delta": "0",
  "executionPrice": "0.0000",
  "feeGrowthGlobal0X128Delta": "5507930424444982259736347157352787128931407551935325049",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -887272,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "26037782196502120275425782622539039026",
  "poolBalance1": "1",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token0 for token1 to price 0.40000 1`] = `
Object {
  "poolBalance0": "26037782196502120275425782622539039026",
  "poolBalance1": "1",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "26037782196502120275425782622539039026",
  "poolBalance1": "1",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-1000",
  "amount1Before": "1",
  "amount1Delta": "2",
  "executionPrice": "0.0020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000059000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -880303,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "1",
  "amount1Delta": "2",
  "executionPrice": "0.0000000000000000020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000059000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -880303,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "1",
  "amount1Delta": "2",
  "executionPrice": "0.0000000000000000020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000059000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -880303,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token1 for token0 to price 0.40000 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-26037782196502120272263504962370659661",
  "amount1Before": "1",
  "amount1Delta": "1268717215714495283",
  "executionPrice": "0.000000000000000000048726",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "647583145675012958539816297734564973",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": -9164,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests close to min price swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "26037782196502120275425782622539039026",
  "amount0Delta": "-26037782196502120274160871558471687260",
  "amount1Before": "1",
  "amount1Delta": "3171793039286238112",
  "executionPrice": "0.00000000000000000012182",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1618957864187523634078592530170978294",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000059000",
  "tickAfter": 9163,
  "tickBefore": -880303,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-989",
  "executionPrice": "0.98900",
  "feeGrowthGlobal0X128Delta": "1701411834604692317316",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-989",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000",
  "executionPrice": "1.0111",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1701411834604692317316",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-662207357859531772",
  "executionPrice": "0.66221",
  "feeGrowthGlobal0X128Delta": "1701411834604692317316873037158841057",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.44742",
  "poolPriceBefore": "1.0000",
  "tickAfter": -8043,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "836795075501202120",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70004",
  "feeGrowthGlobal0X128Delta": "1423733044596672457631004491657125052",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-662207357859531772",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.5101",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1701411834604692317316873037158841057",
  "poolPriceAfter": "2.2350",
  "poolPriceBefore": "1.0000",
  "tickAfter": 8042,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "836795075501202120",
  "executionPrice": "1.4285",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1423733044596672457631004491657125052",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1012",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000",
  "executionPrice": "0.98814",
  "feeGrowthGlobal0X128Delta": "1871553018065161549048",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "2020202020202020203",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.49500",
  "feeGrowthGlobal0X128Delta": "3437195625464025050172418213103875650",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.25000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -13864,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "836795075501202120",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70004",
  "feeGrowthGlobal0X128Delta": "1423733044596672457631004491657125052",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1174017838553918518",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-735088935932648267",
  "executionPrice": "0.62613",
  "feeGrowthGlobal0X128Delta": "1997487844552658120479227965844634309",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1012",
  "executionPrice": "1.0120",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1871553018065161549048",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "2020202020202020203",
  "executionPrice": "2.0202",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "3437195625464025050172418213103875650",
  "poolPriceAfter": "4.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 13863,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "836795075501202120",
  "executionPrice": "1.4285",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1423733044596672457631004491657125052",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests high fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-735088935932648267",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1174017838553918518",
  "executionPrice": "1.5971",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1997487844552658120479227965844634309",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1000",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-36792225529204286454178948640580261338",
  "executionPrice": "3.6792e+34",
  "feeGrowthGlobal0X128Delta": "2381976568446569244235",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.1734e+30",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 705098,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-36796311329002736528533367667012547243",
  "executionPrice": "3.6796e+19",
  "feeGrowthGlobal0X128Delta": "510423550381413479995299567101531162",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.0241",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 13923,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-36796311329002736528533367667012547243",
  "executionPrice": "3.6796e+19",
  "feeGrowthGlobal0X128Delta": "510423550381413479995299567101531162",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "4.0241",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 13923,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "2",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-1000",
  "executionPrice": "500.00",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.3849e+38",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 887219,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "2",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "5.0000e+17",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.3849e+38",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 887219,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "2",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "5.0000e+17",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.3849e+38",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 887219,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "3171793039286238109",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-36796311329002736531280492711270170687",
  "executionPrice": "1.1601e+19",
  "feeGrowthGlobal0X128Delta": "1618957864187523123655042148763283097",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": -9164,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token0 for token1 to price 2.5000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1268717215714495281",
  "amount1Before": "36796311329002736532545403775337522448",
  "amount1Delta": "-36796311329002736529383126115169143088",
  "executionPrice": "2.9003e+19",
  "feeGrowthGlobal0X128Delta": "647583145675012618257449376796101507",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "3.4026e+38",
  "tickAfter": 9163,
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the max ratio swap token1 for token0 to price 2.5000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "36796311329002736532545403775337522448",
  "poolPriceBefore": "3.4026e+38",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 887271,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-36792226666449146913445651103694411508",
  "amount1Before": "0",
  "amount1Delta": "1000",
  "executionPrice": "0.000000000000000000000000000000000027180",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "2381976568446569244235",
  "poolPriceAfter": "0.00000000000000000000000000000023974",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -705093,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-36796311322104302058426248623781044040",
  "amount1Before": "0",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.000000000000000000027177",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381413820277666488039994629",
  "poolPriceAfter": "0.24850",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -13924,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-36796311322104302058426248623781044040",
  "amount1Before": "0",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.000000000000000000027177",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381413820277666488039994629",
  "poolPriceAfter": "0.24850",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -13924,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token0 for token1 to price 0.40000 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "36796311322104302062438284732106019258",
  "poolBalance1": "0",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-1000",
  "amount1Before": "0",
  "amount1Delta": "2",
  "executionPrice": "0.0020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029543",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -887220,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "0",
  "amount1Delta": "2",
  "executionPrice": "0.0000000000000000020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029543",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -887220,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "0",
  "amount1Delta": "2",
  "executionPrice": "0.0000000000000000020000",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029543",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -887220,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token1 for token0 to price 0.40000 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-36796311322104302059276007071937639893",
  "amount1Before": "0",
  "amount1Delta": "1268717215714495283",
  "executionPrice": "0.000000000000000000034479",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "647583145675012958539816297734564973",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": -9164,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests initialized at the min ratio swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "36796311322104302062438284732106019258",
  "amount0Delta": "-36796311322104302061173373668038667492",
  "amount1Before": "0",
  "amount1Delta": "3171793039286238112",
  "executionPrice": "0.000000000000000000086199",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1618957864187523634078592530170978294",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "0.0000000000000000000000000000000000000029390",
  "tickAfter": 9163,
  "tickBefore": -887272,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-998",
  "executionPrice": "0.99800",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-998",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000",
  "executionPrice": "1.0020",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-666444407401233536",
  "executionPrice": "0.66644",
  "feeGrowthGlobal0X128Delta": "85070591730234956148210572796405514",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.44459",
  "poolPriceBefore": "1.0000",
  "tickAfter": -8107,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "828841545518949575",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904950",
  "executionPrice": "0.70675",
  "feeGrowthGlobal0X128Delta": "70510040727899606087499539976421836",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-666444407401233536",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.5005",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85070591730234956148210572796405515",
  "poolPriceAfter": "2.2493",
  "poolPriceBefore": "1.0000",
  "tickAfter": 8106,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904950",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "828841545518949574",
  "executionPrice": "1.4149",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "70510040727899435946316079507190105",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1002",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000",
  "executionPrice": "0.99800",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "2001000500250125077",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.49975",
  "feeGrowthGlobal0X128Delta": "170226296608774038574344664756091446",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.25000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -13864,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "828841545518949575",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904950",
  "executionPrice": "0.70675",
  "feeGrowthGlobal0X128Delta": "70510040727899606087499539976421836",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1162859089713235953",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-735088935932648266",
  "executionPrice": "0.63214",
  "feeGrowthGlobal0X128Delta": "98925110860787308007692432636113977",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1002",
  "executionPrice": "1.0020",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "2001000500250125079",
  "executionPrice": "2.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170226296608774378856711585694554910",
  "poolPriceAfter": "4.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 13863,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904950",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "828841545518949574",
  "executionPrice": "1.4149",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "70510040727899435946316079507190105",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-735088935932648266",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1162859089713235954",
  "executionPrice": "1.5819",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "98925110860787308007692432636113978",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000",
  "amount1Before": "999700069986003",
  "amount1Delta": "-998",
  "executionPrice": "0.99800",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-998",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000",
  "executionPrice": "1.0020",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000700370186095",
  "amount1Before": "999700069986003",
  "amount1Delta": "-999700069986002",
  "executionPrice": "0.99900",
  "feeGrowthGlobal0X128Delta": "85130172636557991529041720559172",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000700370186095",
  "amount1Before": "999700069986003",
  "amount1Delta": "-999700069986002",
  "executionPrice": "0.99900",
  "feeGrowthGlobal0X128Delta": "85130172636557991529041720559172",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-999700069986002",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000700370186095",
  "executionPrice": "1.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85130172636557991529041720559172",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-999700069986002",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000700370186095",
  "executionPrice": "1.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85130172636557991529041720559172",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1002",
  "amount1Before": "999700069986003",
  "amount1Delta": "-1000",
  "executionPrice": "0.99800",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000700370186095",
  "amount1Before": "999700069986003",
  "amount1Delta": "-999700069986002",
  "executionPrice": "0.99900",
  "feeGrowthGlobal0X128Delta": "85130172636557991529041720559172",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000700370186095",
  "amount1Before": "999700069986003",
  "amount1Delta": "-999700069986002",
  "executionPrice": "0.99900",
  "feeGrowthGlobal0X128Delta": "85130172636557991529041720559172",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "1000700370186095",
  "amount1Before": "999700069986003",
  "amount1Delta": "-999700069986002",
  "executionPrice": "0.99900",
  "feeGrowthGlobal0X128Delta": "85130172636557991529041720559172",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "999700069986003",
  "poolBalance1": "999700069986003",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-1000",
  "amount1Before": "999700069986003",
  "amount1Delta": "1002",
  "executionPrice": "1.0020",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-999700069986002",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000700370186095",
  "executionPrice": "1.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85130172636557991529041720559172",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-999700069986002",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000700370186095",
  "executionPrice": "1.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85130172636557991529041720559172",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "999700069986003",
  "poolBalance1": "999700069986003",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests low fee, large liquidity around current price (stable swap) swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "999700069986003",
  "amount0Delta": "-999700069986002",
  "amount1Before": "999700069986003",
  "amount1Delta": "1000700370186095",
  "executionPrice": "1.0010",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "85130172636557991529041720559172",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "1000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "0",
  "executionPrice": "0.0000",
  "feeGrowthGlobal0X128Delta": "29575000",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "0",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "1000",
  "executionPrice": "-Infinity",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "29575000",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-996999999999999318",
  "executionPrice": "0.99700",
  "feeGrowthGlobal0X128Delta": "88725000000017597125",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-996999999999999318",
  "executionPrice": "0.99700",
  "feeGrowthGlobal0X128Delta": "88725000000017597125",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-996999999999999232",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.0030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "88725000000020140575",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-996999999999999232",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.0030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "88725000000020140575",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "145660",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-1000",
  "executionPrice": "0.0068653",
  "feeGrowthGlobal0X128Delta": "12924275",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "1003009027081361181",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.99700",
  "feeGrowthGlobal0X128Delta": "88991975927793784300",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "1003009027081361181",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.99700",
  "feeGrowthGlobal0X128Delta": "88991975927793784300",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "6706554036096900675845906992672697",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "-4228872409409224753601131225116702",
  "executionPrice": "0.63056",
  "feeGrowthGlobal0X128Delta": "595039006852697512464428097924911949",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "11505743598341114571255423385623647",
  "poolBalance1": "11505743598341114571255423385506404",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-1000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "145660",
  "executionPrice": "145.66",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "12924275",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "1003009027081361094",
  "executionPrice": "1.0030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "88991975927793784300",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "1003009027081361094",
  "executionPrice": "1.0030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "88991975927793784300",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "11505743598341114571255423385623647",
  "poolBalance1": "11505743598341114571255423385506404",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests max full range liquidity at 1:1 price with default fee swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "11505743598341114571255423385623647",
  "amount0Delta": "-4228872409409224753601131224936259",
  "amount1Before": "11505743598341114571255423385506404",
  "amount1Delta": "6706554036096900675845906992220230",
  "executionPrice": "1.5859",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "595039006852697512464428097884749099",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "1000",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-991",
  "executionPrice": "0.99100",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.99402",
  "poolPriceBefore": "1.0000",
  "tickAfter": -61,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-991",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "1000",
  "executionPrice": "1.0091",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "1.0060",
  "poolPriceBefore": "1.0000",
  "tickAfter": 60,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-662011820624678025",
  "executionPrice": "0.66201",
  "feeGrowthGlobal0X128Delta": "510423550381407695195061911147652317",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.44355",
  "poolPriceBefore": "1.0000",
  "tickAfter": -8130,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "824893095908431542",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-579795727715083389",
  "executionPrice": "0.70287",
  "feeGrowthGlobal0X128Delta": "421044862698692740725170743495410672",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-662011820624678025",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.5105",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195061911147652317",
  "poolPriceAfter": "2.2545",
  "poolPriceBefore": "1.0000",
  "tickAfter": 8129,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-579795727715083389",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "824893095908431542",
  "executionPrice": "1.4227",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "421044862698692740725170743495410672",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "1011",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-1000",
  "executionPrice": "0.98912",
  "feeGrowthGlobal0X128Delta": "680564733841876926926",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.99402",
  "poolPriceBefore": "1.0000",
  "tickAfter": -61,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "2024171064311638316",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.49403",
  "feeGrowthGlobal0X128Delta": "1033184581225259164735720748018047287",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.24701",
  "poolPriceBefore": "1.0000",
  "tickAfter": -13984,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "824893095908431542",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-579795727715083389",
  "executionPrice": "0.70287",
  "feeGrowthGlobal0X128Delta": "421044862698692740725170743495410672",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "1159748196632793863",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "-729098226020826705",
  "executionPrice": "0.62867",
  "feeGrowthGlobal0X128Delta": "591962792073745646583043420635066071",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "1994009290088178439",
  "poolBalance1": "1994009290088178439",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-1000",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "1011",
  "executionPrice": "1.0110",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "680564733841876926926",
  "poolPriceAfter": "1.0060",
  "poolPriceBefore": "1.0000",
  "tickAfter": 60,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "2024171064311638316",
  "executionPrice": "2.0242",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1033184581225259164735720748018047287",
  "poolPriceAfter": "4.0484",
  "poolPriceBefore": "1.0000",
  "tickAfter": 13983,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-579795727715083389",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "824893095908431542",
  "executionPrice": "1.4227",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "421044862698692740725170743495410672",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "1994009290088178439",
  "poolBalance1": "1994009290088178439",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 0 liquidity, all liquidity around current price swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "1994009290088178439",
  "amount0Delta": "-729098226020826705",
  "amount1Before": "1994009290088178439",
  "amount1Delta": "1159748196632793863",
  "executionPrice": "1.5907",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "591962792073745646583043420635066071",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-996",
  "executionPrice": "0.99600",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-996",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000",
  "executionPrice": "1.0040",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-665331998665331998",
  "executionPrice": "0.66533",
  "feeGrowthGlobal0X128Delta": "510423550381407695195061911147652317",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.44533",
  "poolPriceBefore": "1.0000",
  "tickAfter": -8090,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "830919884399388263",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70499",
  "feeGrowthGlobal0X128Delta": "424121077477644648929101317621422688",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-665331998665331998",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.5030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195061911147652317",
  "poolPriceAfter": "2.2455",
  "poolPriceBefore": "1.0000",
  "tickAfter": 8089,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "830919884399388263",
  "executionPrice": "1.4185",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "424121077477644648929101317621422688",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1005",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000",
  "executionPrice": "0.99502",
  "feeGrowthGlobal0X128Delta": "680564733841876926926",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "2006018054162487463",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.49850",
  "feeGrowthGlobal0X128Delta": "1023918857334819954209013958517557896",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.25000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -13864,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "830919884399388263",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70499",
  "feeGrowthGlobal0X128Delta": "424121077477644648929101317621422688",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "1165774985123750584",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "-735088935932648267",
  "executionPrice": "0.63056",
  "feeGrowthGlobal0X128Delta": "595039006852697554786973994761078087",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1005",
  "executionPrice": "1.0050",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "680564733841876926926",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "2006018054162487463",
  "executionPrice": "2.0060",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1023918857334819954209013958517557896",
  "poolPriceAfter": "4.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 13863,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "830919884399388263",
  "executionPrice": "1.4185",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "424121077477644648929101317621422688",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "2000000000000000000",
  "poolBalance1": "2000000000000000000",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, 2e18 max range liquidity swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "2000000000000000000",
  "amount0Delta": "-735088935932648267",
  "amount1Before": "2000000000000000000",
  "amount1Delta": "1165774985123750584",
  "executionPrice": "1.5859",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "595039006852697554786973994761078087",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-996",
  "executionPrice": "0.99600",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-996",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1000",
  "executionPrice": "1.0040",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-795933705287758544",
  "executionPrice": "0.79593",
  "feeGrowthGlobal0X128Delta": "256749882580179971840679703106063897",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.63923",
  "poolPriceBefore": "1.0000",
  "tickAfter": -4476,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-795933705287758544",
  "executionPrice": "0.79593",
  "feeGrowthGlobal0X128Delta": "256749882580179971840679703106063897",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.63923",
  "poolPriceBefore": "1.0000",
  "tickAfter": -4476,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-795933705287758544",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.2564",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "256749882580179971840679703106063897",
  "poolPriceAfter": "1.5644",
  "poolPriceBefore": "1.0000",
  "tickAfter": 4475,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-795933705287758544",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.2564",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "256749882580179971840679703106063897",
  "poolPriceAfter": "1.5644",
  "poolPriceBefore": "1.0000",
  "tickAfter": 4475,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1005",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-1000",
  "executionPrice": "0.99502",
  "feeGrowthGlobal0X128Delta": "680564733841876926926",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1342022152495072924",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.74514",
  "feeGrowthGlobal0X128Delta": "344037963272993171369654596359692757",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.56026",
  "poolPriceBefore": "1.0000",
  "tickAfter": -5794,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "1342022152495072924",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.74514",
  "feeGrowthGlobal0X128Delta": "344037963272993171369654596359692757",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.56026",
  "poolPriceBefore": "1.0000",
  "tickAfter": -5794,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "2325523181756544449",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "-1464187161953474971",
  "executionPrice": "0.62962",
  "feeGrowthGlobal0X128Delta": "595039006852697724928157455230309818",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "3994009290088178439",
  "poolBalance1": "3994009290088178439",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-1000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1005",
  "executionPrice": "1.0050",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "680564733841876926926",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1342022152495072924",
  "executionPrice": "1.3420",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "344037963272993171369654596359692757",
  "poolPriceAfter": "1.7849",
  "poolPriceBefore": "1.0000",
  "tickAfter": 5793,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "1342022152495072924",
  "executionPrice": "1.3420",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "344037963272993171369654596359692757",
  "poolPriceAfter": "1.7849",
  "poolPriceBefore": "1.0000",
  "tickAfter": 5793,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "3994009290088178439",
  "poolBalance1": "3994009290088178439",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:1 price, additional liquidity around current price swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "3994009290088178439",
  "amount0Delta": "-1464187161953474971",
  "amount1Before": "3994009290088178439",
  "amount1Delta": "2325523181756544449",
  "executionPrice": "1.5883",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "595039006852697724928157455230309818",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "1000",
  "amount1Before": "632455532033675867",
  "amount1Delta": "-99",
  "executionPrice": "0.099000",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.10000",
  "poolPriceBefore": "0.10000",
  "tickAfter": -23028,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-9969",
  "amount1Before": "632455532033675867",
  "amount1Delta": "1000",
  "executionPrice": "0.10031",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "0.10000",
  "poolPriceBefore": "0.10000",
  "tickAfter": -23028,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "632455532033675867",
  "amount1Delta": "-86123526743846551",
  "executionPrice": "0.086124",
  "feeGrowthGlobal0X128Delta": "510423550381407695195061911147652317",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.074620",
  "poolPriceBefore": "0.10000",
  "tickAfter": -25955,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "6324555320336758664",
  "poolBalance1": "632455532033675867",
  "poolPriceBefore": "0.10000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-3869747612262812753",
  "amount1Before": "632455532033675867",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.25841",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407865336245371616884047",
  "poolPriceAfter": "0.66378",
  "poolPriceBefore": "0.10000",
  "tickAfter": -4099,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-3869747612262812753",
  "amount1Before": "632455532033675867",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "0.25841",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407865336245371616884047",
  "poolPriceAfter": "0.66378",
  "poolPriceBefore": "0.10000",
  "tickAfter": -4099,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "10032",
  "amount1Before": "632455532033675867",
  "amount1Delta": "-1000",
  "executionPrice": "0.099681",
  "feeGrowthGlobal0X128Delta": "5274376687274546183682",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.10000",
  "poolPriceBefore": "0.10000",
  "tickAfter": -23028,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "36907032419362389223785084665766560335",
  "amount1Before": "632455532033675867",
  "amount1Delta": "-632455532033675838",
  "executionPrice": "0.000000000000000000017136",
  "feeGrowthGlobal0X128Delta": "18838218521532665615644565874197034349094564536667752274",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "0.10000",
  "tickAfter": -887272,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "poolBalance0": "6324555320336758664",
  "poolBalance1": "632455532033675867",
  "poolPriceBefore": "0.10000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token0 for token1 to price 0.40000 1`] = `
Object {
  "poolBalance0": "6324555320336758664",
  "poolBalance1": "632455532033675867",
  "poolPriceBefore": "0.10000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "6324555320336758664",
  "poolBalance1": "632455532033675867",
  "poolPriceBefore": "0.10000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-1000",
  "amount1Before": "632455532033675867",
  "amount1Delta": "102",
  "executionPrice": "0.10200",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "170141183460469231731",
  "poolPriceAfter": "0.10000",
  "poolPriceBefore": "0.10000",
  "tickAfter": -23028,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "632455532033675867",
  "amount1Delta": "119138326055954425",
  "executionPrice": "0.11914",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "60811007371978153949466126675899993",
  "poolPriceAfter": "0.14109",
  "poolPriceBefore": "0.10000",
  "tickAfter": -19585,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "632455532033675867",
  "amount1Delta": "119138326055954425",
  "executionPrice": "0.11914",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "60811007371978153949466126675899993",
  "poolPriceAfter": "0.14109",
  "poolPriceBefore": "0.10000",
  "tickAfter": -19585,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token1 for token0 to price 0.40000 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-3162277660168379331",
  "amount1Before": "632455532033675867",
  "amount1Delta": "634358607857247611",
  "executionPrice": "0.20060",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "323791572837503501799197590655727195",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "0.10000",
  "tickAfter": -9164,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 1:10 price, 2e18 max range liquidity swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "6324555320336758664",
  "amount0Delta": "-5059644256269406930",
  "amount1Before": "632455532033675867",
  "amount1Delta": "2537434431428990440",
  "executionPrice": "0.50150",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1295166291350014177337973823092140516",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "0.10000",
  "tickAfter": 9163,
  "tickBefore": -23028,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "1000",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-9969",
  "executionPrice": "9.9690",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "10.000",
  "poolPriceBefore": "10.000",
  "tickAfter": 23027,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "-99",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "1000",
  "executionPrice": "10.101",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "10.000",
  "poolPriceBefore": "10.000",
  "tickAfter": 23027,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-3869747612262812754",
  "executionPrice": "3.8697",
  "feeGrowthGlobal0X128Delta": "510423550381407865336245371616884048",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.5065",
  "poolPriceBefore": "10.000",
  "tickAfter": 4098,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-3869747612262812754",
  "executionPrice": "3.8697",
  "feeGrowthGlobal0X128Delta": "510423550381407865336245371616884048",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.5065",
  "poolPriceBefore": "10.000",
  "tickAfter": 4098,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "-86123526743846551",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "11.611",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195061911147652317",
  "poolPriceAfter": "13.401",
  "poolPriceBefore": "10.000",
  "tickAfter": 25954,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "632455532033675867",
  "poolBalance1": "6324555320336758664",
  "poolPriceBefore": "10.000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "102",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-1000",
  "executionPrice": "9.8039",
  "feeGrowthGlobal0X128Delta": "170141183460469231731",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "10.000",
  "poolPriceBefore": "10.000",
  "tickAfter": 23027,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "119138326055954425",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "8.3936",
  "feeGrowthGlobal0X128Delta": "60811007371978153949466126675899993",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "7.0877",
  "poolPriceBefore": "10.000",
  "tickAfter": 19584,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "119138326055954425",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "8.3936",
  "feeGrowthGlobal0X128Delta": "60811007371978153949466126675899993",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "7.0877",
  "poolPriceBefore": "10.000",
  "tickAfter": 19584,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "2537434431428990438",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-5059644256269406930",
  "executionPrice": "1.9940",
  "feeGrowthGlobal0X128Delta": "1295166291350014007196790362622908786",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "10.000",
  "tickAfter": -9164,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token0 for token1 to price 2.5000 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "634358607857247610",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "-3162277660168379331",
  "executionPrice": "4.9850",
  "feeGrowthGlobal0X128Delta": "323791572837503501799197590655727196",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "10.000",
  "tickAfter": 9163,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "-1000",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "10032",
  "executionPrice": "10.032",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "5274376687274546183682",
  "poolPriceAfter": "10.000",
  "poolPriceBefore": "10.000",
  "tickAfter": 23027,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "632455532033675867",
  "amount0Delta": "-632455532033675838",
  "amount1Before": "6324555320336758664",
  "amount1Delta": "36907032426281581270030941278837275671",
  "executionPrice": "5.8355e+19",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "18838218525064384185660173270402201838945341643205005201",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "10.000",
  "tickAfter": 887271,
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "poolBalance0": "632455532033675867",
  "poolBalance1": "6324555320336758664",
  "poolPriceBefore": "10.000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "632455532033675867",
  "poolBalance1": "6324555320336758664",
  "poolPriceBefore": "10.000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, 10:1 price, 2e18 max range liquidity swap token1 for token0 to price 2.5000 1`] = `
Object {
  "poolBalance0": "632455532033675867",
  "poolBalance1": "6324555320336758664",
  "poolPriceBefore": "10.000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 23027,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-996",
  "amount1Before": "0",
  "amount1Delta": "1000",
  "executionPrice": "1.0040",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-665331998665331998",
  "amount1Before": "0",
  "amount1Delta": "1000000000000000000",
  "executionPrice": "1.5030",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "510423550381407695195061911147652317",
  "poolPriceAfter": "2.2455",
  "poolPriceBefore": "1.0000",
  "tickAfter": 8089,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "0",
  "amount1Delta": "830919884399388263",
  "executionPrice": "1.4185",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "424121077477644648929101317621422688",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.0000000000000000000000000000000000000029390",
  "poolPriceBefore": "1.0000",
  "tickAfter": -887272,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "0",
  "amount1Before": "0",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "1995041008271423675",
  "poolBalance1": "0",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-1000",
  "amount1Before": "0",
  "amount1Delta": "1005",
  "executionPrice": "1.0050",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "680564733841876926926",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 0,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-1000000000000000000",
  "amount1Before": "0",
  "amount1Delta": "2006018054162487463",
  "executionPrice": "2.0060",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "1023918857334819954209013958517557896",
  "poolPriceAfter": "4.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 13863,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-585786437626904951",
  "amount1Before": "0",
  "amount1Delta": "830919884399388263",
  "executionPrice": "1.4185",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "424121077477644648929101317621422688",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "1995041008271423675",
  "poolBalance1": "0",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token0 liquidity only swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "1995041008271423675",
  "amount0Delta": "-735088935932648267",
  "amount1Before": "0",
  "amount1Delta": "1165774985123750584",
  "executionPrice": "1.5859",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "595039006852697554786973994761078087",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 0.0000000000000010000 token0 for token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1000",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-996",
  "executionPrice": "0.99600",
  "feeGrowthGlobal0X128Delta": "510423550381407695195",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 0.0000000000000010000 token1 for token0 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 1.0000 token0 for token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1000000000000000000",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-665331998665331998",
  "executionPrice": "0.66533",
  "feeGrowthGlobal0X128Delta": "510423550381407695195061911147652317",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.44533",
  "poolPriceBefore": "1.0000",
  "tickAfter": -8090,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 1.0000 token0 for token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "830919884399388263",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70499",
  "feeGrowthGlobal0X128Delta": "424121077477644648929101317621422688",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 1.0000 token1 for token0 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap exactly 1.0000 token1 for token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token0 for exactly 0.0000000000000010000 token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1005",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-1000",
  "executionPrice": "0.99502",
  "feeGrowthGlobal0X128Delta": "680564733841876926926",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "1.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -1,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token0 for exactly 1.0000 token1 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "2006018054162487463",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-1000000000000000000",
  "executionPrice": "0.49850",
  "feeGrowthGlobal0X128Delta": "1023918857334819954209013958517557896",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.25000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -13864,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token0 for exactly 1.0000 token1 to price 0.50000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "830919884399388263",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-585786437626904951",
  "executionPrice": "0.70499",
  "feeGrowthGlobal0X128Delta": "424121077477644648929101317621422688",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.50000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -6932,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token0 for token1 to price 0.40000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "1165774985123750584",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "-735088935932648267",
  "executionPrice": "0.63056",
  "feeGrowthGlobal0X128Delta": "595039006852697554786973994761078087",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "0.40000",
  "poolPriceBefore": "1.0000",
  "tickAfter": -9164,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token0 for token1 to price 2.5000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "1995041008271423675",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token1 for exactly 0.0000000000000010000 token0 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token1 for exactly 1.0000 token0 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "3.4026e+38",
  "poolPriceBefore": "1.0000",
  "tickAfter": 887271,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token1 for exactly 1.0000 token0 to price 2.0000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.0000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 6931,
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token1 for token0 to price 0.40000 1`] = `
Object {
  "poolBalance0": "0",
  "poolBalance1": "1995041008271423675",
  "poolPriceBefore": "1.0000",
  "swapError": "VM Exception while processing transaction: reverted with reason string 'SPL'",
  "tickBefore": 0,
}
`;

exports[`UniswapV3Pool swap tests medium fee, token1 liquidity only swap token1 for token0 to price 2.5000 1`] = `
Object {
  "amount0Before": "0",
  "amount0Delta": "0",
  "amount1Before": "1995041008271423675",
  "amount1Delta": "0",
  "executionPrice": "NaN",
  "feeGrowthGlobal0X128Delta": "0",
  "feeGrowthGlobal1X128Delta": "0",
  "poolPriceAfter": "2.5000",
  "poolPriceBefore": "1.0000",
  "tickAfter": 9163,
  "tickBefore": 0,
}
`;
