// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn entire position after some time passes 1`] = `94105`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn when only position using ticks 1`] = `94105`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price entire position burn but other positions are using the ticks 1`] = `93279`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price partial position burn 1`] = `98079`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn entire position after some time passes 1`] = `111392`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn when only position using ticks 1`] = `106340`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price entire position burn but other positions are using the ticks 1`] = `98172`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price partial position burn 1`] = `102972`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn entire position after some time passes 1`] = `103578`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn when only position using ticks 1`] = `103578`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price entire position burn but other positions are using the ticks 1`] = `93920`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price partial position burn 1`] = `98720`;

exports[`UniswapV3Pool gas tests fee is off #collect close to worst case 1`] = `46688`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext grow by 1 slot 1`] = `51153`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext no op 1`] = `24740`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position after some time passes 1`] = `110993`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position existing 1`] = `110993`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price new position mint first in range 1`] = `229651`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price second position in same range 1`] = `128093`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position after some time passes 1`] = `150068`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position existing 1`] = `140951`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price new position mint first in range 1`] = `331180`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price second position in same range 1`] = `158051`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position after some time passes 1`] = `111621`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position existing 1`] = `111621`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price new position mint first in range 1`] = `311387`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price second position in same range 1`] = `128721`;

exports[`UniswapV3Pool gas tests fee is off #poke best case 1`] = `52238`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick above 1`] = `29882`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick below 1`] = `29844`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick inside 1`] = `37231`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `116883`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block with no tick movement 1`] = `101049`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `142099`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `195856`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `131620`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `195856`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `215056`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `116883`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block with no tick movement 1`] = `101160`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `128681`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `182405`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn entire position after some time passes 1`] = `94105`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn when only position using ticks 1`] = `94105`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price entire position burn but other positions are using the ticks 1`] = `93279`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price partial position burn 1`] = `98079`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn entire position after some time passes 1`] = `111392`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn when only position using ticks 1`] = `106340`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price entire position burn but other positions are using the ticks 1`] = `98172`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price partial position burn 1`] = `102972`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn entire position after some time passes 1`] = `103578`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn when only position using ticks 1`] = `103578`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price entire position burn but other positions are using the ticks 1`] = `93920`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price partial position burn 1`] = `98720`;

exports[`UniswapV3Pool gas tests fee is on #collect close to worst case 1`] = `46688`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext grow by 1 slot 1`] = `51153`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext no op 1`] = `24740`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position after some time passes 1`] = `110993`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position existing 1`] = `110993`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price new position mint first in range 1`] = `229651`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price second position in same range 1`] = `128093`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position after some time passes 1`] = `150068`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position existing 1`] = `140951`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price new position mint first in range 1`] = `331180`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price second position in same range 1`] = `158051`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position after some time passes 1`] = `111621`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position existing 1`] = `111621`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price new position mint first in range 1`] = `311387`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price second position in same range 1`] = `128721`;

exports[`UniswapV3Pool gas tests fee is on #poke best case 1`] = `52238`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick above 1`] = `29882`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick below 1`] = `29844`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick inside 1`] = `37231`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `122270`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block with no tick movement 1`] = `106289`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `147633`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `201831`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `137301`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `201831`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `221031`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `122270`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block with no tick movement 1`] = `106400`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `134068`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `188233`;
