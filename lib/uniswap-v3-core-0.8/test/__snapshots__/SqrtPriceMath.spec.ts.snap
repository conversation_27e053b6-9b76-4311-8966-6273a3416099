// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SqrtPriceMath #getAmount0Delta gas cost for amount0 where roundUp = true 1`] = `606`;

exports[`SqrtPriceMath #getAmount0Delta gas cost for amount0 where roundUp = true 2`] = `486`;

exports[`SqrtPriceMath #getAmount1Delta gas cost for amount0 where roundUp = false 1`] = `486`;

exports[`SqrtPriceMath #getAmount1Delta gas cost for amount0 where roundUp = true 1`] = `606`;

exports[`SqrtPriceMath #getNextSqrtPriceFromInput zeroForOne = false gas 1`] = `562`;

exports[`SqrtPriceMath #getNextSqrtPriceFromInput zeroForOne = true gas 1`] = `761`;

exports[`SqrtPriceMath #getNextSqrtPriceFromOutput zeroForOne = false gas 1`] = `856`;

exports[`SqrtPriceMath #getNextSqrtPriceFromOutput zeroForOne = true gas 1`] = `429`;
