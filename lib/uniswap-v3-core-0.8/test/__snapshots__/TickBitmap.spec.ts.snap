// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TickBitmap #flipTick gas cost of flipping a tick that results in deleting a word 1`] = `22206`;

exports[`TickBitmap #flipTick gas cost of flipping first tick in word to initialized 1`] = `44118`;

exports[`TickBitmap #flipTick gas cost of flipping second tick in word to initialized 1`] = `27018`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = false gas cost for entire word 1`] = `2572`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = false gas cost just below boundary 1`] = `2572`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = false gas cost on boundary 1`] = `2572`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = true gas cost for entire word 1`] = `2570`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = true gas cost just below boundary 1`] = `2880`;

exports[`TickBitmap #nextInitializedTickWithinOneWord lte = true gas cost on boundary 1`] = `2570`;
