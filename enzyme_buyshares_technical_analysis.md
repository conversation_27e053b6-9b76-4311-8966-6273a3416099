# Enzyme Protocol - Детальный технический анализ процесса buyShares

## 1. Полная трассировка вызовов функций

### 1.1 Начальная точка входа

**Функция:** `ComptrollerProxy.buyShares(uint256 _investmentAmount, uint256 _minSharesQuantity)`
- **Контракт:** ComptrollerLib.sol (через ComptrollerProxy)
- **Параметры:**
  - `_investmentAmount`: Количество denomination asset для инвестирования
  - `_minSharesQuantity`: Минимальное количество долей для получения
- **Возвращает:** `sharesReceived_` (uint256) - фактическое количество полученных долей

```solidity
function buyShares(uint256 _investmentAmount, uint256 _minSharesQuantity)
    external
    override
    returns (uint256 sharesReceived_)
{
    bool hasSharesActionTimelock = getSharesActionTimelock() > 0;
    address canonicalSender = __msgSender();

    return __buyShares(
        canonicalSender, _investmentAmount, _minSharesQuantity, hasSharesActionTimelock, canonicalSender
    );
}
```

### 1.2 Основная логика выполнения

**Функция:** `__buyShares(address _buyer, uint256 _investmentAmount, uint256 _minSharesQuantity, bool _hasSharesActionTimelock, address _canonicalSender)`
- **Модификатор:** `locksReentrance` - защита от реентрантности
- **Ключевые проверки:**

#### 1.2.1 Валидация параметров
```solidity
require(_minSharesQuantity > 0, "__buyShares: _minSharesQuantity must be >0");
```

#### 1.2.2 Проверка состояния фонда
```solidity
address vaultProxyCopy = getVaultProxy();
require(
    !_hasSharesActionTimelock || !__hasPendingMigrationOrReconfiguration(vaultProxyCopy),
    "__buyShares: Pending migration or reconfiguration"
);
```

### 1.3 Расчет GAV (Gross Asset Value)

**Функция:** `calcGav()`
- **Контракт:** ComptrollerLib.sol
- **Процесс:**
  1. Получение всех tracked assets из VaultProxy
  2. Получение всех active external positions
  3. Расчет балансов всех активов
  4. Вызов ValueInterpreter для оценки активов
  5. Добавление стоимости external positions

```solidity
function calcGav() public override returns (uint256 gav_) {
    address vaultProxyAddress = getVaultProxy();
    address[] memory assets = IVault(vaultProxyAddress).getTrackedAssets();
    address[] memory externalPositions = IVault(vaultProxyAddress).getActiveExternalPositions();

    if (assets.length == 0 && externalPositions.length == 0) {
        return 0;
    }

    uint256[] memory balances = new uint256[](assets.length);
    for (uint256 i; i < assets.length; i++) {
        balances[i] = IERC20(assets[i]).balanceOf(vaultProxyAddress);
    }

    gav_ = IValueInterpreter(getValueInterpreter()).calcCanonicalAssetsTotalValue(
        assets, balances, getDenominationAsset()
    );

    if (externalPositions.length > 0) {
        for (uint256 i; i < externalPositions.length; i++) {
            uint256 externalPositionValue = __calcExternalPositionValue(externalPositions[i]);
            gav_ += externalPositionValue;
        }
    }

    return gav_;
}
```

### 1.4 Pre-Buy Shares Hook

**Функция:** `__preBuySharesHook(address _buyer, uint256 _investmentAmount, uint256 _gav)`
- **Вызов:** `FeeManager.invokeHook(FeeHook.PreBuyShares, encodedData, gav)`
- **Цель:** Позволяет комиссиям выполнить логику до выпуска долей

### 1.5 Обработка протокольных комиссий

#### 1.5.1 Оплата протокольной комиссии
**Функция:** `VaultProxy.payProtocolFee()`
- **Процесс:** Выпуск долей в пользу ProtocolFeeReserve

#### 1.5.2 Автоматический выкуп протокольных долей (опционально)
**Функция:** `__buyBackMaxProtocolFeeShares(address _vaultProxy, uint256 _gav)`
- **Условие:** Если включен `autoProtocolFeeSharesBuyback`

### 1.6 Перевод активов

**Функция:** `__transferFromWithReceivedAmount(address _asset, address _sender, address _recipient, uint256 _transferAmount)`
- **Процесс:**
  1. Сохранение баланса получателя до перевода
  2. Выполнение `IERC20.safeTransferFrom()`
  3. Расчет фактически полученной суммы (для токенов с комиссией за перевод)

```solidity
function __transferFromWithReceivedAmount(
    address _asset,
    address _sender,
    address _recipient,
    uint256 _transferAmount
) private returns (uint256 receivedAmount_) {
    uint256 preTransferRecipientBalance = IERC20(_asset).balanceOf(_recipient);
    
    IERC20(_asset).safeTransferFrom(_sender, _recipient, _transferAmount);
    
    return IERC20(_asset).balanceOf(_recipient) - preTransferRecipientBalance;
}
```

### 1.7 Расчет цены долей и количества к выпуску

#### 1.7.1 Расчет цены доли
**Функция:** `__calcGrossShareValue(uint256 _gav, uint256 _sharesSupply, uint256 _denominationAssetUnit)`
- **Формула:**
  - Если `_sharesSupply == 0`: возвращает `_denominationAssetUnit` (1:1 для первого депозита)
  - Иначе: `_gav * SHARES_UNIT / _sharesSupply`

```solidity
function __calcGrossShareValue(uint256 _gav, uint256 _sharesSupply, uint256 _denominationAssetUnit)
    private
    pure
    returns (uint256 grossShareValue_)
{
    if (_sharesSupply == 0) {
        return _denominationAssetUnit;
    }

    return _gav * SHARES_UNIT / _sharesSupply;
}
```

#### 1.7.2 Расчет количества долей к выпуску
**Формула:** `sharesIssued = receivedInvestmentAmount * SHARES_UNIT / sharePrice`
- **Константа:** `SHARES_UNIT = 10^18`

### 1.8 Выпуск долей

**Функция:** `VaultProxy.mintShares(address _target, uint256 _amount)`
- **Контракт:** VaultLib.sol
- **Процесс:** Вызов внутренней функции `__mint()` из OpenZeppelin ERC20

```solidity
function mintShares(address _target, uint256 _amount) external override onlyAccessor {
    __mint(_target, _amount);
}
```

### 1.9 Post-Buy Shares Hook

**Функция:** `__postBuySharesHook(address _buyer, uint256 _investmentAmount, uint256 _sharesIssued, uint256 _preBuySharesGav)`
- **Процессы:**
  1. **FeeManager Hook:** `FeeManager.invokeHook(FeeHook.PostBuyShares, encodedData, updatedGav)`
  2. **Policy Validation:** `PolicyManager.validatePolicies(PolicyHook.PostBuyShares, encodedData)`

### 1.10 Финальные проверки и обновления

#### 1.10.1 Проверка минимального количества долей
```solidity
sharesReceived_ = IERC20(vaultProxyCopy).balanceOf(_buyer) - prevBuyerShares;
require(sharesReceived_ >= _minSharesQuantity, "__buyShares: Shares received < _minSharesQuantity");
```

#### 1.10.2 Обновление timelock (если включен)
```solidity
if (_hasSharesActionTimelock) {
    acctToLastSharesBoughtTimestamp[_buyer] = block.timestamp;
}
```

#### 1.10.3 Эмиссия события
```solidity
emit SharesBought(_buyer, receivedInvestmentAmount, sharesIssued, sharesReceived_);
```

## 2. Ключевые константы и параметры

- **SHARES_UNIT:** `10^18` - базовая единица для расчета долей
- **ONE_HUNDRED_PERCENT:** `10000` - 100% в базисных пунктах
- **Reentrancy Protection:** Модификатор `locksReentrance`
- **Shares Action Timelock:** Опциональная блокировка после покупки долей

## 3. Детальный анализ взаимодействий

### 3.1 Взаимодействие с ValueInterpreter

**Функция:** `ValueInterpreter.calcCanonicalAssetsTotalValue(address[] assets, uint256[] amounts, address quoteAsset)`
- **Цель:** Конвертация всех активов фонда в denomination asset
- **Процесс:**
  1. Для каждого актива вызывается `calcCanonicalAssetValue()`
  2. Используются Chainlink price feeds через ChainlinkPriceFeedMixin
  3. Суммируется общая стоимость в denomination asset

### 3.2 Обработка External Positions

**Функция:** `__calcExternalPositionValue(address _externalPosition)`
- **Процесс:**
  1. Получение managed assets: `IExternalPosition.getManagedAssets()`
  2. Получение debt assets: `IExternalPosition.getDebtAssets()`
  3. Расчет чистой стоимости: `managedValue - debtValue`

```solidity
function __calcExternalPositionValue(address _externalPosition) private returns (uint256 value_) {
    (address[] memory managedAssets, uint256[] memory managedAmounts) =
        IExternalPosition(_externalPosition).getManagedAssets();

    uint256 managedValue = IValueInterpreter(getValueInterpreter()).calcCanonicalAssetsTotalValue(
        managedAssets, managedAmounts, getDenominationAsset()
    );

    (address[] memory debtAssets, uint256[] memory debtAmounts) =
        IExternalPosition(_externalPosition).getDebtAssets();

    uint256 debtValue = IValueInterpreter(getValueInterpreter()).calcCanonicalAssetsTotalValue(
        debtAssets, debtAmounts, getDenominationAsset()
    );

    if (managedValue > debtValue) {
        value_ = managedValue - debtValue;
    }

    return value_;
}
```

### 3.3 Fee Manager Hook System

#### 3.3.1 PreBuyShares Hook
- **Timing:** До перевода активов и выпуска долей
- **Цель:** Позволяет комиссиям рассчитать и взыскать входные комиссии
- **Encoded Data:** `abi.encode(_buyer, _investmentAmount)`

#### 3.3.2 PostBuyShares Hook
- **Timing:** После выпуска долей
- **Цель:** Обновление состояния комиссий, взыскание performance fees
- **Encoded Data:** `abi.encode(_buyer, _investmentAmount, _sharesIssued)`
- **Updated GAV:** `_preBuySharesGav + _investmentAmount`

### 3.4 Policy Manager Validation

**Функция:** `PolicyManager.validatePolicies(address _comptrollerProxy, PolicyHook _hook, bytes _validationData)`
- **Hook:** `PolicyHook.PostBuyShares`
- **Validation Data:** `abi.encode(_buyer, _investmentAmount, _sharesIssued, gav)`
- **Примеры политик:**
  - Minimum investment amount
  - Maximum fund size
  - Investor whitelist
  - Geographic restrictions

## 4. Математические формулы и расчеты

### 4.1 Расчет цены доли
```
Если totalSupply == 0:
    sharePrice = denominationAssetUnit (обычно 10^18 для ETH/USDC)
Иначе:
    sharePrice = (GAV * SHARES_UNIT) / totalSupply
    где SHARES_UNIT = 10^18
```

### 4.2 Расчет количества долей к выпуску
```
sharesIssued = (receivedInvestmentAmount * SHARES_UNIT) / sharePrice
```

### 4.3 Пример расчета
```
Дано:
- GAV = 1,000,000 USDC (10^6 * 10^6)
- totalSupply = 500,000 shares (5 * 10^5 * 10^18)
- investmentAmount = 10,000 USDC (10^4 * 10^6)

Расчет:
sharePrice = (1,000,000 * 10^6 * 10^18) / (500,000 * 10^18) = 2 * 10^6 (2 USDC per share)
sharesIssued = (10,000 * 10^6 * 10^18) / (2 * 10^6) = 5,000 * 10^18 shares
```

## 5. Критические точки безопасности

1. **Reentrancy Protection:** Модификатор `locksReentrance` предотвращает реентрантные атаки
2. **Price Oracle Security:** Зависимость от ValueInterpreter и Chainlink price feeds
3. **Slippage Protection:** Проверка `_minSharesQuantity` защищает от front-running
4. **Fee Processing Order:** Корректная последовательность обработки комиссий
5. **Timelock Mechanism:** Защита от MEV-атак через shares action timelock
6. **Migration Protection:** Блокировка операций во время миграции/реконфигурации
7. **Transfer Fee Handling:** Корректная обработка токенов с комиссией за перевод

## 6. Потенциальные векторы атак

1. **Price Manipulation:** Манипуляция GAV через flash loans перед покупкой долей
2. **Front-running:** Опережающие транзакции для извлечения MEV
3. **Sandwich Attacks:** Атаки типа "сэндвич" вокруг крупных инвестиций
4. **Fee Exploitation:** Эксплуатация логики комиссий для извлечения стоимости
5. **Oracle Attacks:** Атаки на price feeds для искажения GAV
