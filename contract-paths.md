# Пути к контрактам Enzyme Finance

Ниже представлены предполагаемые пути к контрактам, упомянутым в scope.md. Для получения точных путей рекомендуется использовать команду поиска в репозитории:

## Основные контракты (Core)
- ComptrollerLib - `contracts/release/core/ComptrollerLib.sol`
- Dispatcher - `contracts/persistent/dispatcher/Dispatcher.sol`
- FundDeployer - `contracts/release/core/FundDeployer.sol`
- VaultLib - `contracts/release/core/VaultLib.sol`

## Менеджеры и расширения
- ExternalPositionFactory - `contracts/release/extensions/external-position-manager/ExternalPositionFactory.sol`
- ExternalPositionManager - `contracts/release/extensions/external-position-manager/ExternalPositionManager.sol`
- FeeManager - `contracts/release/extensions/fee-manager/FeeManager.sol`
- IntegrationManager - `contracts/release/extensions/integration-manager/IntegrationManager.sol`
- PolicyManager - `contracts/release/extensions/policy-manager/PolicyManager.sol`

## Комиссии
- EntranceRateBurnFee - `contracts/release/extensions/fee-manager/fees/EntranceRateBurnFee.sol`
- EntranceRateDirectFee - `contracts/release/extensions/fee-manager/fees/EntranceRateDirectFee.sol`
- ExitRateBurnFee - `contracts/release/extensions/fee-manager/fees/ExitRateBurnFee.sol`
- ExitRateDirectFee - `contracts/release/extensions/fee-manager/fees/ExitRateDirectFee.sol`
- ManagementFee - `contracts/release/extensions/fee-manager/fees/ManagementFee.sol`
- PerformanceFee - `contracts/release/extensions/fee-manager/fees/PerformanceFee.sol`
- MinSharesSupplyFee - `contracts/release/extensions/fee-manager/fees/MinSharesSupplyFee.sol`

## Политики
- AllowedAdapterIncomingAssetsPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdapterIncomingAssetsPolicy.sol`
- AllowedAdaptersPerManagerPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdaptersPerManagerPolicy.sol`
- AllowedAdaptersPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdaptersPolicy.sol`
- AllowedAssetsForRedemptionPolicy - `contracts/release/extensions/policy-manager/policies/redemption/AllowedAssetsForRedemptionPolicy.sol`
- AllowedDepositRecipientsPolicy - `contracts/release/extensions/policy-manager/policies/deposit/AllowedDepositRecipientsPolicy.sol`
- AllowedExternalPositionTypesPerManagerPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedExternalPositionTypesPerManagerPolicy.sol`
- AllowedExternalPositionTypesPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedExternalPositionTypesPolicy.sol`
- AllowedSharesTransferRecipientsPolicy - `contracts/release/extensions/policy-manager/policies/shares/AllowedSharesTransferRecipientsPolicy.sol`
- CumulativeSlippageTolerancePolicy - `contracts/release/extensions/policy-manager/policies/trading/CumulativeSlippageTolerancePolicy.sol`
- MinAssetBalancesPostRedemptionPolicy - `contracts/release/extensions/policy-manager/policies/redemption/MinAssetBalancesPostRedemptionPolicy.sol`
- MinMaxInvestmentPolicy - `contracts/release/extensions/policy-manager/policies/deposit/MinMaxInvestmentPolicy.sol`
- OnlyRemoveDustExternalPositionPolicy - `contracts/release/extensions/policy-manager/policies/utils/OnlyRemoveDustExternalPositionPolicy.sol`
- OnlyUntrackDustOrPricelessAssetsPolicy - `contracts/release/extensions/policy-manager/policies/utils/OnlyUntrackDustOrPricelessAssetsPolicy.sol`
- NoDepegOnRedeemSharesForSpecificAssetsPolicy - `contracts/release/extensions/policy-manager/policies/redemption/NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol`

## Адаптеры
- AaveV2Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/AaveV2Adapter.sol`
- AaveV3Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/AaveV3Adapter.sol`
- CurveLiquidityAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/CurveLiquidityAdapter.sol`
- EnzymeV4VaultAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/EnzymeV4VaultAdapter.sol`
- OneInchV5Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/OneInchV5Adapter.sol`
- ParaSwapV5Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/ParaSwapV5Adapter.sol`
- ParaSwapV6Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/ParaSwapV6Adapter.sol`
- StaderStakingAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/StaderStakingAdapter.sol`
- ThreeOneThirdAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/ThreeOneThirdAdapter.sol`
- TransferAssetsAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/TransferAssetsAdapter.sol`
- YearnVaultV2Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/YearnVaultV2Adapter.sol`
- ZeroExV4Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/ZeroExV4Adapter.sol`

## Внешние позиции
- AaveDebtPositionLib - `contracts/release/extensions/external-position-manager/external-positions/aave-debt/AaveDebtPositionLib.sol`
- AaveDebtPositionParser - `contracts/release/extensions/external-position-manager/external-positions/aave-debt/AaveDebtPositionParser.sol`
- AaveV3DebtPositionLib - `contracts/release/extensions/external-position-manager/external-positions/aave-v3-debt/AaveV3DebtPositionLib.sol`
- AaveV3DebtPositionParser - `contracts/release/extensions/external-position-manager/external-positions/aave-v3-debt/AaveV3DebtPositionParser.sol`
- AlicePositionLib - `contracts/release/extensions/external-position-manager/external-positions/alice/AlicePositionLib.sol`
- AlicePositionParser - `contracts/release/extensions/external-position-manager/external-positions/alice/AlicePositionParser.sol`
- ArbitraryLoanPositionLib - `contracts/release/extensions/external-position-manager/external-positions/arbitrary-loan/ArbitraryLoanPositionLib.sol`
- ArbitraryLoanPositionParser - `contracts/release/extensions/external-position-manager/external-positions/arbitrary-loan/ArbitraryLoanPositionParser.sol`
- GMXV2LeverageTradingPositionLib - `contracts/release/extensions/external-position-manager/external-positions/gmx-v2-leverage-trading/GMXV2LeverageTradingPositionLib.sol`
- GMXV2LeverageTradingPositionParser - `contracts/release/extensions/external-position-manager/external-positions/gmx-v2-leverage-trading/GMXV2LeverageTradingPositionParser.sol`
- MorphoBluePositionLib - `contracts/release/extensions/external-position-manager/external-positions/morpho-blue/MorphoBluePositionLib.sol`
- MorphoBluePositionParser - `contracts/release/extensions/external-position-manager/external-positions/morpho-blue/MorphoBluePositionParser.sol`
- StaderWithdrawalsPositionLib - `contracts/release/extensions/external-position-manager/external-positions/stader-withdrawals/StaderWithdrawalsPositionLib.sol`
- StaderWithdrawalsPositionParser - `contracts/release/extensions/external-position-manager/external-positions/stader-withdrawals/StaderWithdrawalsPositionParser.sol`
- TheGraphDelegationPositionLib - `contracts/release/extensions/external-position-manager/external-positions/the-graph-delegation/TheGraphDelegationPositionLib.sol`
- TheGraphDelegationPositionParser - `contracts/release/extensions/external-position-manager/external-positions/the-graph-delegation/TheGraphDelegationPositionParser.sol`
- UniswapV3LiquidityPositionLib - `contracts/release/extensions/external-position-manager/external-positions/uniswap-v3-liquidity/UniswapV3LiquidityPositionLib.sol`
- UniswapV3LiquidityPositionParser - `contracts/release/extensions/external-position-manager/external-positions/uniswap-v3-liquidity/UniswapV3LiquidityPositionParser.sol`

## Инфраструктура и ценовые фиды
- AavePriceFeed - `contracts/release/infrastructure/price-feeds/primitives/AavePriceFeed.sol`
- BalancerV2GaugeTokenPriceFeed - `contracts/release/infrastructure/price-feeds/primitives/BalancerV2GaugeTokenPriceFeed.sol`
- BalancerV2StablePoolPriceFeed - `contracts/release/infrastructure/price-feeds/primitives/BalancerV2StablePoolPriceFeed.sol`
- BalancerV2WeightedPoolPriceFeed - `contracts/release/infrastructure/price-feeds/primitives/BalancerV2WeightedPoolPriceFeed.sol`
- ConvertedQuoteAggregatorFactory - `contracts/release/infrastructure/price-feeds/derivatives/feeds/ConvertedQuoteAggregatorFactory.sol`
- CurvePriceFeed - `contracts/release/infrastructure/price-feeds/primitives/CurvePriceFeed.sol`
- ERC4626PriceFeed - `contracts/release/infrastructure/price-feeds/primitives/ERC4626PriceFeed.sol`
- ERC4626RateAggregatorFactory - `contracts/release/infrastructure/price-feeds/derivatives/feeds/ERC4626RateAggregatorFactory.sol`
- EnzymeVaultPriceFeed - `contracts/release/infrastructure/price-feeds/primitives/EnzymeVaultPriceFeed.sol`
- FundValueCalculator - `contracts/release/infrastructure/value-interpreter/FundValueCalculator.sol`
- FundValueCalculatorRouter - `contracts/release/infrastructure/value-interpreter/FundValueCalculatorRouter.sol`
- ManualValueOracleFactory - `contracts/release/infrastructure/price-feeds/derivatives/feeds/ManualValueOracleFactory.sol`
- PeggedDerivativesPriceFeed - `contracts/release/infrastructure/price-feeds/primitives/PeggedDerivativesPriceFeed.sol`
- PeggedRateDeviationAggregatorFactory - `contracts/release/infrastructure/price-feeds/derivatives/feeds/PeggedRateDeviationAggregatorFactory.sol`
- SmarDexUsdnNativeRateUsdAggregator - `contracts/release/infrastructure/price-feeds/derivatives/feeds/SmarDexUsdnNativeRateUsdAggregator.sol`
- SolvBtcYieldTokenRateUsdAggregatorFactory - `contracts/release/infrastructure/price-feeds/derivatives/feeds/SolvBtcYieldTokenRateUsdAggregatorFactory.sol`
- UsdEthSimulatedAggregator - `contracts/release/infrastructure/price-feeds/derivatives/feeds/UsdEthSimulatedAggregator.sol`
- ValueInterpreter - `contracts/release/infrastructure/value-interpreter/ValueInterpreter.sol`
- YearnVaultV2PriceFeed - `contracts/release/infrastructure/price-feeds/primitives/YearnVaultV2PriceFeed.sol`

## Периферийные контракты
- DepositWrapper - `contracts/release/peripheral/DepositWrapper.sol`
- UnpermissionedActionsWrapper - `contracts/release/peripheral/UnpermissionedActionsWrapper.sol`

## Очереди и фабрики
- AaveV3FlashLoanAssetManagerFactory - `contracts/release/extensions/asset-managers/AaveV3FlashLoanAssetManagerFactory.sol`
- AaveV3FlashLoanAssetManagerLib - `contracts/release/extensions/asset-managers/AaveV3FlashLoanAssetManagerLib.sol`
- GasRelayPaymasterFactory - `contracts/persistent/gas-relayer/GasRelayPaymasterFactory.sol`
- GasRelayPaymasterLib - `contracts/persistent/gas-relayer/GasRelayPaymasterLib.sol`
- GatedRedemptionQueueSharesWrapperFactory - `contracts/release/extensions/shares-wrappers/GatedRedemptionQueueSharesWrapperFactory.sol`
- GatedRedemptionQueueSharesWrapperLib - `contracts/release/extensions/shares-wrappers/GatedRedemptionQueueSharesWrapperLib.sol`
- SharePriceThrottledAssetManagerFactory - `contracts/release/extensions/asset-managers/SharePriceThrottledAssetManagerFactory.sol`
- SharePriceThrottledAssetManagerLib - `contracts/release/extensions/asset-managers/SharePriceThrottledAssetManagerLib.sol`
- SingleAssetDepositQueueFactory - `contracts/release/extensions/deposit-wrappers/SingleAssetDepositQueueFactory.sol`
- SingleAssetDepositQueueLib - `contracts/release/extensions/deposit-wrappers/SingleAssetDepositQueueLib.sol`
- SingleAssetRedemptionQueueFactory - `contracts/release/extensions/redemption-wrappers/SingleAssetRedemptionQueueFactory.sol`
- SingleAssetRedemptionQueueLib - `contracts/release/extensions/redemption-wrappers/SingleAssetRedemptionQueueLib.sol`

## Реестры и конфигурация
- AddressListRegistry - `contracts/persistent/address-list-registry/AddressListRegistry.sol`
- AaveV2ATokenListOwner - `contracts/release/infrastructure/price-feeds/primitives/AaveV2ATokenListOwner.sol`
- AaveV3ATokenListOwner - `contracts/release/infrastructure/price-feeds/primitives/AaveV3ATokenListOwner.sol`
- CompoundV3CTokenListOwner - `contracts/release/infrastructure/price-feeds/primitives/CompoundV3CTokenListOwner.sol`
- GlobalConfigLib - `contracts/persistent/global-config/GlobalConfigLib.sol`
- GlobalConfigProxy - `contracts/persistent/global-config/GlobalConfigProxy.sol`
- ProtocolFeeReserveLib - `contracts/release/infrastructure/protocol-fee/ProtocolFeeReserveLib.sol`
- ProtocolFeeReserveProxy - `contracts/release/infrastructure/protocol-fee/ProtocolFeeReserveProxy.sol`
- ProtocolFeeTracker - `contracts/release/infrastructure/protocol-fee/ProtocolFeeTracker.sol`
- UintListRegistry - `contracts/persistent/uint-list-registry/UintListRegistry.sol`