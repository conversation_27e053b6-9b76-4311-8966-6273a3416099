// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity 0.6.12;

import {IUniswapV2Factory} from "../../../../../external-interfaces/IUniswapV2Factory.sol";
import {IIntegrationManager} from "../../IIntegrationManager.sol";
import {UniswapV2ActionsMixin} from "../utils/0.6.12/actions/UniswapV2ActionsMixin.sol";
import {AdapterBase} from "../utils/0.6.12/AdapterBase.sol";

/// @title UniswapV2LiquidityAdapter Contract
/// <AUTHOR> Foundation <<EMAIL>>
/// @notice Adapter for interacting with Uniswap v2 liquidity provision
contract UniswapV2LiquidityAdapter is AdapterBase, UniswapV2ActionsMixin {
    address private immutable FACTORY;

    constructor(address _integrationManager, address _router, address _factory)
        public
        AdapterBase(_integrationManager)
        UniswapV2ActionsMixin(_router)
    {
        FACTORY = _factory;
    }

    // EXTERNAL FUNCTIONS

    /// @notice Lends assets for pool tokens on Uniswap
    /// @param _vaultProxy The VaultProxy of the calling fund
    /// @param _actionData Data specific to this action
    function lend(address _vaultProxy, bytes calldata _actionData, bytes calldata) external onlyIntegrationManager {
        (
            address[2] memory outgoingAssets,
            uint256[2] memory maxOutgoingAssetAmounts,
            uint256[2] memory minOutgoingAssetAmounts,
        ) = __decodeLendCallArgs(_actionData);

        __uniswapV2Lend(
            _vaultProxy,
            outgoingAssets[0],
            outgoingAssets[1],
            maxOutgoingAssetAmounts[0],
            maxOutgoingAssetAmounts[1],
            minOutgoingAssetAmounts[0],
            minOutgoingAssetAmounts[1]
        );
    }

    /// @notice Redeems pool tokens on Uniswap
    /// @param _vaultProxy The VaultProxy of the calling fund
    /// @param _actionData Data specific to this action
    /// @param _assetData Parsed spend assets and incoming assets data for this action
    function redeem(address _vaultProxy, bytes calldata _actionData, bytes calldata _assetData)
        external
        onlyIntegrationManager
    {
        (uint256 outgoingAssetAmount, address[2] memory incomingAssets, uint256[2] memory minIncomingAssetAmounts) =
            __decodeRedeemCallArgs(_actionData);

        // More efficient to parse pool token from _assetData than external call
        (address[] memory spendAssets,,) = __decodeAssetData(_assetData);

        __uniswapV2Redeem(
            _vaultProxy,
            spendAssets[0],
            outgoingAssetAmount,
            incomingAssets[0],
            incomingAssets[1],
            minIncomingAssetAmounts[0],
            minIncomingAssetAmounts[1]
        );
    }

    /////////////////////////////
    // PARSE ASSETS FOR METHOD //
    /////////////////////////////

    /// @notice Parses the expected assets in a particular action
    /// @param _selector The function selector for the callOnIntegration
    /// @param _actionData Data specific to this action
    /// @return spendAssetsHandleType_ A type that dictates how to handle granting
    /// the adapter access to spend assets (`None` by default)
    /// @return spendAssets_ The assets to spend in the call
    /// @return spendAssetAmounts_ The max asset amounts to spend in the call
    /// @return incomingAssets_ The assets to receive in the call
    /// @return minIncomingAssetAmounts_ The min asset amounts to receive in the call
    function parseAssetsForAction(address, bytes4 _selector, bytes calldata _actionData)
        external
        view
        override
        returns (
            IIntegrationManager.SpendAssetsHandleType spendAssetsHandleType_,
            address[] memory spendAssets_,
            uint256[] memory spendAssetAmounts_,
            address[] memory incomingAssets_,
            uint256[] memory minIncomingAssetAmounts_
        )
    {
        if (_selector == LEND_SELECTOR) {
            return __parseAssetsForLend(_actionData);
        } else if (_selector == REDEEM_SELECTOR) {
            return __parseAssetsForRedeem(_actionData);
        }

        revert("parseAssetsForAction: _selector invalid");
    }

    /// @dev Helper function to parse spend and incoming assets from encoded call args
    /// during lend() calls
    function __parseAssetsForLend(bytes calldata _actionData)
        private
        view
        returns (
            IIntegrationManager.SpendAssetsHandleType spendAssetsHandleType_,
            address[] memory spendAssets_,
            uint256[] memory spendAssetAmounts_,
            address[] memory incomingAssets_,
            uint256[] memory minIncomingAssetAmounts_
        )
    {
        (address[2] memory outgoingAssets, uint256[2] memory maxOutgoingAssetAmounts,, uint256 minIncomingAssetAmount) =
            __decodeLendCallArgs(_actionData);

        spendAssets_ = new address[](2);
        spendAssets_[0] = outgoingAssets[0];
        spendAssets_[1] = outgoingAssets[1];

        spendAssetAmounts_ = new uint256[](2);
        spendAssetAmounts_[0] = maxOutgoingAssetAmounts[0];
        spendAssetAmounts_[1] = maxOutgoingAssetAmounts[1];

        incomingAssets_ = new address[](1);
        // No need to validate not address(0), this will be caught in IntegrationManager
        incomingAssets_[0] = IUniswapV2Factory(FACTORY).getPair(outgoingAssets[0], outgoingAssets[1]);

        minIncomingAssetAmounts_ = new uint256[](1);
        minIncomingAssetAmounts_[0] = minIncomingAssetAmount;

        return (
            IIntegrationManager.SpendAssetsHandleType.Transfer,
            spendAssets_,
            spendAssetAmounts_,
            incomingAssets_,
            minIncomingAssetAmounts_
        );
    }

    /// @dev Helper function to parse spend and incoming assets from encoded call args
    /// during redeem() calls
    function __parseAssetsForRedeem(bytes calldata _actionData)
        private
        view
        returns (
            IIntegrationManager.SpendAssetsHandleType spendAssetsHandleType_,
            address[] memory spendAssets_,
            uint256[] memory spendAssetAmounts_,
            address[] memory incomingAssets_,
            uint256[] memory minIncomingAssetAmounts_
        )
    {
        (uint256 outgoingAssetAmount, address[2] memory incomingAssets, uint256[2] memory minIncomingAssetAmounts) =
            __decodeRedeemCallArgs(_actionData);

        spendAssets_ = new address[](1);
        // No need to validate not address(0), this will be caught in IntegrationManager
        spendAssets_[0] = IUniswapV2Factory(FACTORY).getPair(incomingAssets[0], incomingAssets[1]);

        spendAssetAmounts_ = new uint256[](1);
        spendAssetAmounts_[0] = outgoingAssetAmount;

        incomingAssets_ = new address[](2);
        incomingAssets_[0] = incomingAssets[0];
        incomingAssets_[1] = incomingAssets[1];

        minIncomingAssetAmounts_ = new uint256[](2);
        minIncomingAssetAmounts_[0] = minIncomingAssetAmounts[0];
        minIncomingAssetAmounts_[1] = minIncomingAssetAmounts[1];

        return (
            IIntegrationManager.SpendAssetsHandleType.Transfer,
            spendAssets_,
            spendAssetAmounts_,
            incomingAssets_,
            minIncomingAssetAmounts_
        );
    }

    // PRIVATE FUNCTIONS

    /// @dev Helper to decode the lend encoded call arguments
    function __decodeLendCallArgs(bytes memory _actionData)
        private
        pure
        returns (
            address[2] memory outgoingAssets_,
            uint256[2] memory maxOutgoingAssetAmounts_,
            uint256[2] memory minOutgoingAssetAmounts_,
            uint256 minIncomingAssetAmount_
        )
    {
        return abi.decode(_actionData, (address[2], uint256[2], uint256[2], uint256));
    }

    /// @dev Helper to decode the redeem encoded call arguments
    function __decodeRedeemCallArgs(bytes memory _actionData)
        private
        pure
        returns (
            uint256 outgoingAssetAmount_,
            address[2] memory incomingAssets_,
            uint256[2] memory minIncomingAssetAmounts_
        )
    {
        return abi.decode(_actionData, (uint256, address[2], uint256[2]));
    }

    ///////////////////
    // STATE GETTERS //
    ///////////////////

    /// @notice Gets the `FACTORY` variable
    /// @return factory_ The `FACTORY` variable value
    function getFactory() external view returns (address factory_) {
        return FACTORY;
    }
}
