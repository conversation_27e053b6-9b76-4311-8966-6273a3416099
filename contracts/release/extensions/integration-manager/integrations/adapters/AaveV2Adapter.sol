// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity 0.6.12;

import {AaveV2ActionsMixin} from "../utils/0.6.12/actions/AaveV2ActionsMixin.sol";
import {AaveAdapterBase} from "../utils/0.6.12/bases/AaveAdapterBase.sol";

/// @title AaveV2Adapter Contract
/// <AUTHOR> Foundation <<EMAIL>>
/// @notice Adapter for Aave v2 lending
contract AaveV2Adapter is AaveAdapterBase, AaveV2ActionsMixin {
    constructor(address _integrationManager, address _addressListRegistry, uint256 _aTokenListId, address _lendingPool)
        public
        AaveAdapterBase(_integrationManager, _addressListRegistry, _aTokenListId)
        AaveV2ActionsMixin(_lendingPool)
    {}

    ////////////////////////////////
    // REQUIRED VIRTUAL FUNCTIONS //
    ////////////////////////////////

    /// @dev Logic to lend underlying for aToken
    function __lend(address _vaultProxy, address _underlying, uint256 _amount) internal override {
        __aaveV2Lend({_recipient: _vaultProxy, _underlying: _underlying, _amount: _amount});
    }

    /// @dev Logic to redeem aToken for underlying
    function __redeem(address _vaultProxy, address _underlying, uint256 _amount) internal override {
        __aaveV2Redeem({_recipient: _vaultProxy, _underlying: _underlying, _amount: _amount});
    }
}
