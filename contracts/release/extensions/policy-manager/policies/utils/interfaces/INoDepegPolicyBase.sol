// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity >=0.6.0 <0.9.0;

import {IERC20} from "../../../../../../external-interfaces/IERC20.sol";

/// @title INoDepegPolicyBase Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface INoDepegPolicyBase {
    struct AssetConfig {
        IERC20 asset;
        IERC20 referenceAsset;
        uint16 deviationToleranceInBps;
    }
}
