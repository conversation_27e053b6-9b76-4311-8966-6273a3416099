// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity 0.6.12;
pragma experimental ABIEncoderV2;

import {SafeMath} from "openzeppelin-solc-0.6/math/SafeMath.sol";
import {IERC20} from "../../../../external-interfaces/IERC20.sol";
import {MakerDaoMath} from "../../../../utils/0.6.12/MakerDaoMath.sol";
import {IVault} from "../../../core/fund/vault/IVault.sol";
import {IFeeManager} from "../IFeeManager.sol";
import {IManagementFee} from "./interfaces/IManagementFee.sol";
import {FeeBase} from "./utils/FeeBase.sol";
import {SettableFeeRecipientBase} from "./utils/SettableFeeRecipientBase.sol";
import {UpdatableFeeRecipientBase} from "./utils/UpdatableFeeRecipientBase.sol";

/// @title ManagementFee Contract
/// <AUTHOR> Foundation <<EMAIL>>
/// @notice A management fee with a configurable annual rate
contract ManagementFee is IManagementFee, FeeBase, UpdatableFeeRecipientBase, MakerDaoMath {
    using SafeMath for uint256;

    event ActivatedForMigratedFund(address indexed comptrollerProxy);

    event FundSettingsAdded(address indexed comptrollerProxy, uint128 scaledPerSecondRate);

    event Settled(address indexed comptrollerProxy, uint256 sharesQuantity, uint256 secondsSinceSettlement);

    uint256 private constant RATE_SCALE_BASE = 10 ** 27;

    mapping(address => FeeInfo) private comptrollerProxyToFeeInfo;

    constructor(address _feeManager) public FeeBase(_feeManager) {}

    // EXTERNAL FUNCTIONS

    /// @notice Activates the fee for a fund
    /// @param _comptrollerProxy The ComptrollerProxy of the fund
    /// @param _vaultProxy The VaultProxy of the fund
    function activateForFund(address _comptrollerProxy, address _vaultProxy) external override onlyFeeManager {
        // It is only necessary to set `lastSettled` for a migrated fund
        if (IERC20(_vaultProxy).totalSupply() > 0) {
            comptrollerProxyToFeeInfo[_comptrollerProxy].lastSettled = uint128(block.timestamp);

            emit ActivatedForMigratedFund(_comptrollerProxy);
        }
    }

    /// @notice Add the initial fee settings for a fund
    /// @param _comptrollerProxy The ComptrollerProxy of the fund
    /// @param _settingsData Encoded settings to apply to the fee for a fund
    function addFundSettings(address _comptrollerProxy, bytes calldata _settingsData)
        external
        override
        onlyFeeManager
    {
        (uint128 scaledPerSecondRate, address recipient) = abi.decode(_settingsData, (uint128, address));
        require(scaledPerSecondRate > 0, "addFundSettings: scaledPerSecondRate must be greater than 0");

        comptrollerProxyToFeeInfo[_comptrollerProxy] =
            FeeInfo({scaledPerSecondRate: scaledPerSecondRate, lastSettled: 0});

        emit FundSettingsAdded(_comptrollerProxy, scaledPerSecondRate);

        if (recipient != address(0)) {
            __setRecipientForFund(_comptrollerProxy, recipient);
        }
    }

    /// @notice Settle the fee and calculate shares due
    /// @param _comptrollerProxy The ComptrollerProxy of the fund
    /// @param _vaultProxy The VaultProxy of the fund
    /// @return settlementType_ The type of settlement
    /// @return (unused) The payer of shares due
    /// @return sharesDue_ The amount of shares due
    function settle(address _comptrollerProxy, address _vaultProxy, IFeeManager.FeeHook, bytes calldata, uint256)
        external
        override
        onlyFeeManager
        returns (IFeeManager.SettlementType settlementType_, address, uint256 sharesDue_)
    {
        FeeInfo storage feeInfo = comptrollerProxyToFeeInfo[_comptrollerProxy];

        // If this fee was settled in the current block, we can return early
        uint256 secondsSinceSettlement = block.timestamp.sub(feeInfo.lastSettled);
        if (secondsSinceSettlement == 0) {
            return (IFeeManager.SettlementType.None, address(0), 0);
        }

        // If there are shares issued for the fund, calculate the shares due
        IERC20 sharesToken = IERC20(_vaultProxy);
        uint256 sharesSupply = sharesToken.totalSupply();
        if (sharesSupply > 0) {
            // This assumes that all shares in the VaultProxy are shares outstanding,
            // which is fine for this release. Even if they are not, they are still shares that
            // are only claimable by the fund owner.
            uint256 netSharesSupply = sharesSupply.sub(sharesToken.balanceOf(_vaultProxy));
            if (netSharesSupply > 0) {
                sharesDue_ = netSharesSupply.mul(
                    __rpow(feeInfo.scaledPerSecondRate, secondsSinceSettlement, RATE_SCALE_BASE).sub(RATE_SCALE_BASE)
                ).div(RATE_SCALE_BASE);
            }
        }

        // Must settle even when no shares are due, for the case that settlement is being
        // done when there are no shares in the fund (i.e. at the first investment, or at the
        // first investment after all shares have been redeemed)
        comptrollerProxyToFeeInfo[_comptrollerProxy].lastSettled = uint128(block.timestamp);
        emit Settled(_comptrollerProxy, sharesDue_, secondsSinceSettlement);

        if (sharesDue_ == 0) {
            return (IFeeManager.SettlementType.None, address(0), 0);
        }

        return (IFeeManager.SettlementType.Mint, address(0), sharesDue_);
    }

    /// @notice Gets whether the fee settles and requires GAV on a particular hook
    /// @param _hook The FeeHook
    /// @return settles_ True if the fee settles on the _hook
    /// @return usesGav_ True if the fee uses GAV during settle() for the _hook
    function settlesOnHook(IFeeManager.FeeHook _hook) external view override returns (bool settles_, bool usesGav_) {
        if (
            _hook == IFeeManager.FeeHook.PreBuyShares || _hook == IFeeManager.FeeHook.PreRedeemShares
                || _hook == IFeeManager.FeeHook.Continuous
        ) {
            return (true, false);
        }

        return (false, false);
    }

    // PUBLIC FUNCTIONS

    /// @notice Gets the recipient of the fee for a given fund
    /// @param _comptrollerProxy The ComptrollerProxy contract for the fund
    /// @return recipient_ The recipient
    function getRecipientForFund(address _comptrollerProxy)
        public
        view
        override(FeeBase, SettableFeeRecipientBase)
        returns (address recipient_)
    {
        return SettableFeeRecipientBase.getRecipientForFund(_comptrollerProxy);
    }

    ///////////////////
    // STATE GETTERS //
    ///////////////////

    /// @notice Gets the feeInfo for a given fund
    /// @param _comptrollerProxy The ComptrollerProxy contract of the fund
    /// @return feeInfo_ The feeInfo
    function getFeeInfoForFund(address _comptrollerProxy) external view returns (FeeInfo memory feeInfo_) {
        return comptrollerProxyToFeeInfo[_comptrollerProxy];
    }
}
