// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

import {IMapleV2Globals} from "../../../../../external-interfaces/IMapleV2Globals.sol";
import {IMapleV2Pool} from "../../../../../external-interfaces/IMapleV2Pool.sol";
import {IMapleV2PoolManager} from "../../../../../external-interfaces/IMapleV2PoolManager.sol";
import {IMapleV2ProxyFactory} from "../../../../../external-interfaces/IMapleV2ProxyFactory.sol";
import {IExternalPositionParser} from "../../IExternalPositionParser.sol";
import {IMapleLiquidityPosition} from "./IMapleLiquidityPosition.sol";
import {MapleLiquidityPositionDataDecoder} from "./MapleLiquidityPositionDataDecoder.sol";

pragma solidity 0.6.12;

/// @title MapleLiquidityPositionParser
/// <AUTHOR> Foundation <<EMAIL>>
/// @notice Parser for Maple liquidity positions
contract MapleLiquidityPositionParser is MapleLiquidityPositionDataDecoder, IExternalPositionParser {
    address private immutable MAPLE_V2_GLOBALS;

    constructor(address _mapleV2Globals) public {
        MAPLE_V2_GLOBALS = _mapleV2Globals;
    }

    /// @notice Parses the assets to send and receive for the callOnExternalPosition
    /// @param _actionId The _actionId for the callOnExternalPosition
    /// @param _encodedActionArgs The encoded parameters for the callOnExternalPosition
    /// @return assetsToTransfer_ The assets to be transferred from the Vault
    /// @return amountsToTransfer_ The amounts to be transferred from the Vault
    /// @return assetsToReceive_ The assets to be received at the Vault
    function parseAssetsForAction(address, uint256 _actionId, bytes memory _encodedActionArgs)
        external
        override
        returns (
            address[] memory assetsToTransfer_,
            uint256[] memory amountsToTransfer_,
            address[] memory assetsToReceive_
        )
    {
        if (_actionId == uint256(IMapleLiquidityPosition.Actions.LendV2)) {
            (address pool, uint256 liquidityAssetAmount) = __decodeLendV2ActionArgs(_encodedActionArgs);
            __validatePoolV2(pool);

            assetsToTransfer_ = new address[](1);
            amountsToTransfer_ = new uint256[](1);

            assetsToTransfer_[0] = IMapleV2Pool(pool).asset();
            amountsToTransfer_[0] = liquidityAssetAmount;
        } else if (_actionId == uint256(IMapleLiquidityPosition.Actions.RequestRedeemV2)) {
            (address pool,) = __decodeRequestRedeemV2ActionArgs(_encodedActionArgs);
            __validatePoolV2(pool);
        } else if (_actionId == uint256(IMapleLiquidityPosition.Actions.RedeemV2)) {
            (address pool,) = __decodeRedeemV2ActionArgs(_encodedActionArgs);
            __validatePoolV2(pool);

            assetsToReceive_ = new address[](1);
            assetsToReceive_[0] = IMapleV2Pool(pool).asset();
        } else if (_actionId == uint256(IMapleLiquidityPosition.Actions.CancelRedeemV2)) {
            (address pool,) = __decodeCancelRedeemV2ActionArgs(_encodedActionArgs);
            __validatePoolV2(pool);
        }

        return (assetsToTransfer_, amountsToTransfer_, assetsToReceive_);
    }

    /// @notice Parse and validate input arguments to be used when initializing a newly-deployed ExternalPositionProxy
    /// @return initArgs_ Parsed and encoded args for ExternalPositionProxy.init()
    function parseInitArgs(address, bytes memory) external override returns (bytes memory initArgs_) {
        return "";
    }

    // PRIVATE FUNCTIONS

    // Validates that a pool v2 has been deployed from a Maple factory
    function __validatePoolV2(address _poolV2) private view {
        address poolManager = IMapleV2Pool(_poolV2).manager();
        require(IMapleV2PoolManager(poolManager).pool() == _poolV2, "__validatePoolV2: Invalid PoolManager relation");

        address poolManagerFactory = IMapleV2PoolManager(poolManager).factory();
        require(
            IMapleV2ProxyFactory(poolManagerFactory).isInstance(poolManager),
            "__validatePoolV2: Invalid PoolManagerFactory relation"
        );

        require(
            IMapleV2Globals(MAPLE_V2_GLOBALS).isInstanceOf("POOL_MANAGER_FACTORY", poolManagerFactory),
            "__validatePoolV2: Invalid Globals relation"
        );
    }
}
