// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.
    (c) Enzyme Foundation <<EMAIL>>
    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

import {IExternalPosition} from "../../IExternalPosition.sol";

pragma solidity >=0.6.0 <0.9.0;

/// @title IConvexVotingPosition Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface IConvexVotingPosition is IExternalPosition {
    enum Actions {
        Lock,
        Relock,
        Withdraw,
        ClaimRewards,
        Delegate
    }
}
