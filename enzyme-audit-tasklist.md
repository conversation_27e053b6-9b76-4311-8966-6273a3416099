# Enzyme Finance Audit Task List

## Общие принципы аудита

### Приоритеты проверки:
1. **Критические функции** - создание фонда, депозиты, выкупы, торговля
2. **Управление активами** - расчет стоимости, внешние позиции
3. **Экономическая безопасность** - комиссии, политики, доступ
4. **Системная безопасность** - реентрантность, переполнения, доступ

---

## СЦЕНАРИЙ 1: Создание фонда

### 🔴 Критические проверки
- [ ] **FundDeployer.createNewFund()** - валидация входных параметров
- [ ] **Dispatcher.deployVaultProxy()** - корректность создания VaultProxy
- [ ] **ComptrollerProxy.activate()** - безопасность активации
- [ ] Проверка прав доступа при создании фонда
- [ ] Валидация `_denominationAsset` (не может быть нулевым/недействительным)

### 🟡 Средние проверки  
- [ ] Настройка расширений (FeeManager, PolicyManager)
- [ ] Корректность установки `sharesActionTimelock`
- [ ] Проверка событий при создании фонда
- [ ] Ограничения на имя и символ фонда

### 🟢 Низкие проверки
- [ ] Gas оптимизация процесса создания
- [ ] Совместимость с различными ERC20 токенами как denomination asset

---

## СЦЕНАРИЙ 2: Депозит в фонд

### 🔴 Критические проверки
- [ ] **ComptrollerProxy.buyShares()** - защита от реентрантности
- [ ] **VaultProxy.mintShares()** - корректность выпуска долей
- [ ] Расчет количества долей: `sharesIssued = investmentAmount * SHARES_UNIT / sharePrice`
- [ ] Проверка `_minSharesQuantity` - защита от slippage
- [ ] Валидация политик через PolicyManager.validatePolicies()

### 🟡 Средние проверки
- [ ] **DepositWrapper.depositForFund()** - обмен активов через DEX
- [ ] Обработка входных комиссий (EntranceRateBurnFee, EntranceRateDirectFee)
- [ ] Проверка `sharesActionTimelock` механизма
- [ ] AllowedDepositRecipientsPolicy - ограничения на депозиторов
- [ ] MinMaxInvestmentPolicy - лимиты инвестиций

### 🟢 Низкие проверки
- [ ] Обработка токенов с комиссией за перевод
- [ ] Совместимость с rebasing токенами
- [ ] События при депозите

---

## СЦЕНАРИЙ 3: Торговля активами

### 🔴 Критические проверки
- [ ] **IntegrationManager.callOnIntegration()** - безопасность вызовов адаптеров
- [ ] **VaultProxy.transferAssets()** - корректность передачи активов
- [ ] Проверка политик до и после торговли (Pre/PostCallOnIntegration)
- [ ] Валидация адаптеров через AllowedAdaptersPolicy
- [ ] Защита от sandwich атак через CumulativeSlippageTolerancePolicy

### 🟡 Средние проверки
- [ ] **ParaSwapV5/V6Adapter** - корректность интеграции с ParaSwap
- [ ] **OneInchV5Adapter** - безопасность обменов через 1inch
- [ ] **TransferAssetsAdapter** - простые переводы активов
- [ ] AllowedAdapterIncomingAssetsPolicy - ограничения на входящие активы
- [ ] AllowedAdaptersPerManagerPolicy - права менеджеров на адаптеры

### 🟢 Низкие проверки
- [ ] Gas оптимизация торговых операций
- [ ] Обработка failed транзакций в адаптерах
- [ ] Логирование торговых операций

---

## СЦЕНАРИЙ 4: Управление внешними позициями

### 🔴 Критические проверки
- [ ] **ExternalPositionManager.createExternalPosition()** - безопасность создания
- [ ] **ExternalPositionFactory.create()** - корректность развертывания
- [ ] **ExternalPosition.receiveCallFromVault()** - защита от злоупотреблений
- [ ] Валидация типов внешних позиций
- [ ] Проверка политик для внешних позиций

### 🟡 Средние проверки
- [ ] **MorphoBluePositionLib/Parser** - интеграция с Morpho Blue
- [ ] AllowedExternalPositionTypesPolicy - ограничения типов позиций
- [ ] AllowedExternalPositionTypesPerManagerPolicy - права менеджеров
- [ ] OnlyRemoveDustExternalPositionPolicy - удаление пыли

### 🟢 Низкие проверки
- [ ] Лимиты на количество внешних позиций
- [ ] События при создании/изменении позиций

---

## СЦЕНАРИЙ 5: Выкуп долей фонда

### 🔴 Критические проверки
- [ ] **ComptrollerProxy.redeemShares()** - защита от реентрантности
- [ ] **ComptrollerProxy.redeemSharesForSpecificAssets()** - выкуп конкретными активами
- [ ] **VaultProxy.burnShares()** - корректность сжигания долей
- [ ] Расчет пропорциональных сумм при выкупе
- [ ] Проверка `_minTotalAmountOut` - защита от slippage

### 🟡 Средние проверки
- [ ] Обработка выходных комиссий (ExitRateBurnFee, ExitRateDirectFee)
- [ ] AllowedAssetsForRedemptionPolicy - ограничения на активы выкупа
- [ ] MinAssetBalancesPostRedemptionPolicy - минимальные балансы после выкупа
- [ ] NoDepegOnRedeemSharesForSpecificAssetsPolicy - защита от депега
- [ ] SingleAssetRedemptionQueue - очереди выкупа

### 🟢 Низкие проверки
- [ ] Обработка неликвидных активов при выкупе
- [ ] События при выкупе долей

---

## СЦЕНАРИЙ 6: Расчет стоимости фонда

### 🔴 Критические проверки
- [ ] **FundValueCalculator.calcGav()** - корректность расчета GAV
- [ ] **ValueInterpreter.calcCanonicalAssetValue()** - точность оценки активов
- [ ] Обработка внешних позиций в расчете стоимости
- [ ] Защита от манипуляций ценами через price feeds
- [ ] Обработка неподдерживаемых активов

### 🟡 Средние проверки
- [ ] **CurvePriceFeed** - оценка Curve LP токенов
- [ ] **YearnVaultV2PriceFeed** - оценка Yearn vault токенов
- [ ] **ERC4626PriceFeed** - оценка ERC4626 vault токенов
- [ ] Агрегаторы цен (ERC4626RateAggregatorFactory, PeggedRateDeviationAggregatorFactory)
- [ ] ConvertedQuoteAggregatorFactory - конвертация валют

### 🟢 Низкие проверки
- [ ] Кэширование результатов расчета стоимости
- [ ] Обработка stale цен
- [ ] Fallback механизмы для price feeds

---

## СЦЕНАРИЙ 7: Начисление и выплата комиссий

### 🔴 Критические проверки
- [ ] **FeeManager.invokeHook()** - корректность вызова хуков комиссий
- [ ] **ManagementFee.settle()** - расчет управленческих комиссий
- [ ] **PerformanceFee.settle()** - расчет комиссий за производительность
- [ ] Защита от переполнения при расчете комиссий
- [ ] Корректность выпуска долей как комиссии

### 🟡 Средние проверки
- [ ] Обновление high water mark для PerformanceFee
- [ ] Временные расчеты для ManagementFee
- [ ] Настройка получателей комиссий
- [ ] Различные типы settlement (Direct, Mint, Burn)

### 🟢 Низкие проверки
- [ ] Оптимизация газа при начислении комиссий
- [ ] События при выплате комиссий

---

## ДОПОЛНИТЕЛЬНЫЕ СИСТЕМНЫЕ ПРОВЕРКИ

### 🔴 Безопасность доступа
- [ ] **Модификаторы доступа** - onlyOwner, onlyFundDeployer, onlyAccessor
- [ ] **Права расширений** - проверка isExtension() для критических функций
- [ ] **Временные блокировки** - sharesActionTimelock механизм
- [ ] Проверка msg.sender vs __msgSender() для relay вызовов

### 🔴 Защита от реентрантности
- [ ] **locksReentrance модификатор** - корректность реализации
- [ ] Проверка reentranceLocked флага во всех критических функциях
- [ ] Порядок операций: checks-effects-interactions
- [ ] Защита в external calls к адаптерам и внешним позициям

### 🔴 Математические операции
- [ ] **Переполнения** - использование SafeMath или встроенных проверок
- [ ] **Деление на ноль** - проверки перед делением
- [ ] **Точность расчетов** - использование SHARES_UNIT (10^18)
- [ ] Округления в пользу протокола

### 🔴 Управление состоянием
- [ ] **Инициализация** - корректность init функций
- [ ] **Миграции** - безопасность процесса миграции фондов
- [ ] **Деактивация** - корректность cleanup при деактивации
- [ ] Консистентность состояния между контрактами

### 🟡 Интеграции с внешними протоколами
- [ ] **Aave V3** - debt positions, адаптеры, price feeds
- [ ] **Balancer V2** - liquidity, gauge tokens, price feeds
- [ ] **Uniswap V3** - адаптеры, liquidity positions
- [ ] **Staking протоколы** - Lido, Kiln, Stader withdrawals
- [ ] **ERC4626 vaults** - адаптеры и price feeds

### 🟡 Экономические атаки
- [ ] **Front-running** - защита через slippage параметры
- [ ] **Sandwich атаки** - CumulativeSlippageTolerancePolicy
- [ ] **Price manipulation** - использование TWAP, множественных источников
- [ ] **MEV атаки** - защита в торговых операциях
- [ ] **Governance атаки** - ограничения на изменение параметров

### 🟡 Обработка edge cases
- [ ] **Нулевые значения** - обработка zero amounts, addresses
- [ ] **Максимальные значения** - uint256.max, переполнения
- [ ] **Пустые массивы** - корректная обработка
- [ ] **Дублирующиеся элементы** - проверки uniqueness
- [ ] **Неподдерживаемые токены** - rebasing, fee-on-transfer

### 🟢 Gas оптимизация и DoS
- [ ] **Gas limits** - защита от DoS через gas exhaustion
- [ ] **Loops** - ограничения на размер массивов
- [ ] **Storage operations** - оптимизация SSTORE/SLOAD
- [ ] **External calls** - минимизация и оптимизация

### 🟢 Мониторинг и логирование
- [ ] **События** - полнота и корректность event emission
- [ ] **Error messages** - информативность revert сообщений
- [ ] **State getters** - доступность для мониторинга
- [ ] **Debugging** - возможность трассировки операций

---

## МЕТОДОЛОГИЯ ТЕСТИРОВАНИЯ

### Статический анализ
- [ ] Slither анализ всех scope контрактов
- [ ] Mythril проверка на известные уязвимости
- [ ] Проверка соответствия стандартам (ERC20, ERC4626)

### Динамическое тестирование
- [ ] Unit тесты для каждой критической функции
- [ ] Integration тесты для сценариев end-to-end
- [ ] Fuzzing тесты для edge cases
- [ ] Stress тесты с большими объемами данных

### Экономическое тестирование
- [ ] Симуляция различных рыночных условий
- [ ] Тестирование при экстремальной волатильности
- [ ] Проверка арбитражных возможностей
- [ ] Анализ MEV возможностей

---

## ПРИОРИТИЗАЦИЯ ЗАДАЧ

### Неделя 1: Критические функции
1. Создание фонда и активация
2. Депозиты и выпуск долей
3. Выкупы и сжигание долей
4. Базовая торговля через адаптеры

### Неделя 2: Расширенная функциональность
1. Внешние позиции и их управление
2. Расчет стоимости и price feeds
3. Система комиссий
4. Политики и ограничения

### Неделя 3: Системная безопасность
1. Реентрантность и доступ
2. Математические операции
3. Интеграции с внешними протоколами
4. Edge cases и error handling

### Неделя 4: Финализация
1. Экономические атаки и MEV
2. Gas оптимизация
3. Документация находок
4. Рекомендации по улучшению
