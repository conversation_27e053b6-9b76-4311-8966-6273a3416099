name: Lint

on:
  push:
    branches:
      - main
      - 0.8
  pull_request:

jobs:
  run-linters:
    name: Run linters
    runs-on: ubuntu-latest

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v2

      - name: Set up node
        uses: actions/setup-node@v1
        with:
          node-version: 12

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run linters
        uses: wearerequired/lint-action@a8497ddb33fb1205941fd40452ca9fff07e0770d
        with:
          github_token: ${{ secrets.github_token }}
          prettier: true
          auto_fix: true
          prettier_extensions: 'css,html,js,json,jsx,md,sass,scss,ts,tsx,vue,yaml,yml,sol'
