// SPDX-License-Identifier: MIT
// This file was procedurally generated from scripts/generate/templates/SafeCastMock.js.

pragma solidity ^0.8.0;

import "../utils/math/SafeCast.sol";

contract SafeCastMock {
    using SafeCast for uint256;
    using SafeCast for int256;

    function toUint256(int256 a) public pure returns (uint256) {
        return a.toUint256();
    }

    function toUint248(uint256 a) public pure returns (uint248) {
        return a.toUint248();
    }

    function toUint240(uint256 a) public pure returns (uint240) {
        return a.toUint240();
    }

    function toUint232(uint256 a) public pure returns (uint232) {
        return a.toUint232();
    }

    function toUint224(uint256 a) public pure returns (uint224) {
        return a.toUint224();
    }

    function toUint216(uint256 a) public pure returns (uint216) {
        return a.toUint216();
    }

    function toUint208(uint256 a) public pure returns (uint208) {
        return a.toUint208();
    }

    function toUint200(uint256 a) public pure returns (uint200) {
        return a.toUint200();
    }

    function toUint192(uint256 a) public pure returns (uint192) {
        return a.toUint192();
    }

    function toUint184(uint256 a) public pure returns (uint184) {
        return a.toUint184();
    }

    function toUint176(uint256 a) public pure returns (uint176) {
        return a.toUint176();
    }

    function toUint168(uint256 a) public pure returns (uint168) {
        return a.toUint168();
    }

    function toUint160(uint256 a) public pure returns (uint160) {
        return a.toUint160();
    }

    function toUint152(uint256 a) public pure returns (uint152) {
        return a.toUint152();
    }

    function toUint144(uint256 a) public pure returns (uint144) {
        return a.toUint144();
    }

    function toUint136(uint256 a) public pure returns (uint136) {
        return a.toUint136();
    }

    function toUint128(uint256 a) public pure returns (uint128) {
        return a.toUint128();
    }

    function toUint120(uint256 a) public pure returns (uint120) {
        return a.toUint120();
    }

    function toUint112(uint256 a) public pure returns (uint112) {
        return a.toUint112();
    }

    function toUint104(uint256 a) public pure returns (uint104) {
        return a.toUint104();
    }

    function toUint96(uint256 a) public pure returns (uint96) {
        return a.toUint96();
    }

    function toUint88(uint256 a) public pure returns (uint88) {
        return a.toUint88();
    }

    function toUint80(uint256 a) public pure returns (uint80) {
        return a.toUint80();
    }

    function toUint72(uint256 a) public pure returns (uint72) {
        return a.toUint72();
    }

    function toUint64(uint256 a) public pure returns (uint64) {
        return a.toUint64();
    }

    function toUint56(uint256 a) public pure returns (uint56) {
        return a.toUint56();
    }

    function toUint48(uint256 a) public pure returns (uint48) {
        return a.toUint48();
    }

    function toUint40(uint256 a) public pure returns (uint40) {
        return a.toUint40();
    }

    function toUint32(uint256 a) public pure returns (uint32) {
        return a.toUint32();
    }

    function toUint24(uint256 a) public pure returns (uint24) {
        return a.toUint24();
    }

    function toUint16(uint256 a) public pure returns (uint16) {
        return a.toUint16();
    }

    function toUint8(uint256 a) public pure returns (uint8) {
        return a.toUint8();
    }

    function toInt256(uint256 a) public pure returns (int256) {
        return a.toInt256();
    }

    function toInt248(int256 a) public pure returns (int248) {
        return a.toInt248();
    }

    function toInt240(int256 a) public pure returns (int240) {
        return a.toInt240();
    }

    function toInt232(int256 a) public pure returns (int232) {
        return a.toInt232();
    }

    function toInt224(int256 a) public pure returns (int224) {
        return a.toInt224();
    }

    function toInt216(int256 a) public pure returns (int216) {
        return a.toInt216();
    }

    function toInt208(int256 a) public pure returns (int208) {
        return a.toInt208();
    }

    function toInt200(int256 a) public pure returns (int200) {
        return a.toInt200();
    }

    function toInt192(int256 a) public pure returns (int192) {
        return a.toInt192();
    }

    function toInt184(int256 a) public pure returns (int184) {
        return a.toInt184();
    }

    function toInt176(int256 a) public pure returns (int176) {
        return a.toInt176();
    }

    function toInt168(int256 a) public pure returns (int168) {
        return a.toInt168();
    }

    function toInt160(int256 a) public pure returns (int160) {
        return a.toInt160();
    }

    function toInt152(int256 a) public pure returns (int152) {
        return a.toInt152();
    }

    function toInt144(int256 a) public pure returns (int144) {
        return a.toInt144();
    }

    function toInt136(int256 a) public pure returns (int136) {
        return a.toInt136();
    }

    function toInt128(int256 a) public pure returns (int128) {
        return a.toInt128();
    }

    function toInt120(int256 a) public pure returns (int120) {
        return a.toInt120();
    }

    function toInt112(int256 a) public pure returns (int112) {
        return a.toInt112();
    }

    function toInt104(int256 a) public pure returns (int104) {
        return a.toInt104();
    }

    function toInt96(int256 a) public pure returns (int96) {
        return a.toInt96();
    }

    function toInt88(int256 a) public pure returns (int88) {
        return a.toInt88();
    }

    function toInt80(int256 a) public pure returns (int80) {
        return a.toInt80();
    }

    function toInt72(int256 a) public pure returns (int72) {
        return a.toInt72();
    }

    function toInt64(int256 a) public pure returns (int64) {
        return a.toInt64();
    }

    function toInt56(int256 a) public pure returns (int56) {
        return a.toInt56();
    }

    function toInt48(int256 a) public pure returns (int48) {
        return a.toInt48();
    }

    function toInt40(int256 a) public pure returns (int40) {
        return a.toInt40();
    }

    function toInt32(int256 a) public pure returns (int32) {
        return a.toInt32();
    }

    function toInt24(int256 a) public pure returns (int24) {
        return a.toInt24();
    }

    function toInt16(int256 a) public pure returns (int16) {
        return a.toInt16();
    }

    function toInt8(int256 a) public pure returns (int8) {
        return a.toInt8();
    }
}
