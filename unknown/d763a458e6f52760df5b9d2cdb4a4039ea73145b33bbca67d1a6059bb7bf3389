= Security

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/security

These contracts aim to cover common security practices.

* {PullPayment}: A pattern that can be used to avoid reentrancy attacks.
* {ReentrancyGuard}: A modifier that can prevent reentrancy during certain functions.
* {Pausable}: A common emergency response mechanism that can pause functionality while a remediation is pending.

TIP: For an overview on reentrancy and the possible mechanisms to prevent it, read our article https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].

== Contracts

{{PullPayment}}

{{ReentrancyGuard}}

{{Pausable}}
