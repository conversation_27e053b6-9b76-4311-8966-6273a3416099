// SPDX-License-Identifier: GPL-3.0

/*
    This file is part of the Enzyme Protocol.
    (c) Enzyme Foundation <<EMAIL>>
    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity >=0.6.0 <0.9.0;

/// @title ITheGraphStaking Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface ITheGraphStaking {
    function delegate(address, uint256) external returns (uint256);

    function delegationPools(address) external view returns (uint32, uint32, uint32, uint256, uint256, uint256);

    function getDelegation(address, address) external view returns (uint256, uint256, uint256);

    function undelegate(address, uint256) external returns (uint256);

    function withdrawDelegated(address, address) external returns (uint256);
}
