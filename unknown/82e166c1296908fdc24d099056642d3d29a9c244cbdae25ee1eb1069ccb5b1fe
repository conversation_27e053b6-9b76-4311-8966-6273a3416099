// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn entire position after some time passes 1`] = `58703`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price burn when only position using ticks 1`] = `58703`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price entire position burn but other positions are using the ticks 1`] = `82740`;

exports[`UniswapV3Pool gas tests fee is off #burn above current price partial position burn 1`] = `97740`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn entire position after some time passes 1`] = `69498`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price burn when only position using ticks 1`] = `66309`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price entire position burn but other positions are using the ticks 1`] = `87551`;

exports[`UniswapV3Pool gas tests fee is off #burn around current price partial position burn 1`] = `102551`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn entire position after some time passes 1`] = `64624`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price burn when only position using ticks 1`] = `64624`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price entire position burn but other positions are using the ticks 1`] = `83381`;

exports[`UniswapV3Pool gas tests fee is off #burn below current price partial position burn 1`] = `98381`;

exports[`UniswapV3Pool gas tests fee is off #collect close to worst case 1`] = `35816`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext grow by 1 slot 1`] = `51098`;

exports[`UniswapV3Pool gas tests fee is off #increaseObservationCardinalityNext no op 1`] = `24677`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position after some time passes 1`] = `109351`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price add to position existing 1`] = `109351`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price new position mint first in range 1`] = `228025`;

exports[`UniswapV3Pool gas tests fee is off #mint above current price second position in same range 1`] = `126451`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position after some time passes 1`] = `147718`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price add to position existing 1`] = `138539`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price new position mint first in range 1`] = `328848`;

exports[`UniswapV3Pool gas tests fee is off #mint around current price second position in same range 1`] = `155639`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position after some time passes 1`] = `109943`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price add to position existing 1`] = `109943`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price new position mint first in range 1`] = `309729`;

exports[`UniswapV3Pool gas tests fee is off #mint below current price second position in same range 1`] = `127043`;

exports[`UniswapV3Pool gas tests fee is off #poke best case 1`] = `52006`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick above 1`] = `29643`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick below 1`] = `29605`;

exports[`UniswapV3Pool gas tests fee is off #snapshotCumulativesInside tick inside 1`] = `37053`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `114858`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block with no tick movement 1`] = `99004`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `129871`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `152706`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `129469`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `152706`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `212706`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `114858`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block with no tick movement 1`] = `99115`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `116454`;

exports[`UniswapV3Pool gas tests fee is off #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `139267`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn entire position after some time passes 1`] = `58703`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price burn when only position using ticks 1`] = `58703`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price entire position burn but other positions are using the ticks 1`] = `82740`;

exports[`UniswapV3Pool gas tests fee is on #burn above current price partial position burn 1`] = `97740`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn entire position after some time passes 1`] = `69498`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price burn when only position using ticks 1`] = `66309`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price entire position burn but other positions are using the ticks 1`] = `87551`;

exports[`UniswapV3Pool gas tests fee is on #burn around current price partial position burn 1`] = `102551`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn entire position after some time passes 1`] = `64624`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price burn when only position using ticks 1`] = `64624`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price entire position burn but other positions are using the ticks 1`] = `83381`;

exports[`UniswapV3Pool gas tests fee is on #burn below current price partial position burn 1`] = `98381`;

exports[`UniswapV3Pool gas tests fee is on #collect close to worst case 1`] = `35816`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext grow by 1 slot 1`] = `51098`;

exports[`UniswapV3Pool gas tests fee is on #increaseObservationCardinalityNext no op 1`] = `24677`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position after some time passes 1`] = `109363`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price add to position existing 1`] = `109363`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price new position mint first in range 1`] = `228037`;

exports[`UniswapV3Pool gas tests fee is on #mint above current price second position in same range 1`] = `126463`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position after some time passes 1`] = `147730`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price add to position existing 1`] = `138551`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price new position mint first in range 1`] = `328860`;

exports[`UniswapV3Pool gas tests fee is on #mint around current price second position in same range 1`] = `155651`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position after some time passes 1`] = `109955`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price add to position existing 1`] = `109955`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price new position mint first in range 1`] = `309741`;

exports[`UniswapV3Pool gas tests fee is on #mint below current price second position in same range 1`] = `127055`;

exports[`UniswapV3Pool gas tests fee is on #poke best case 1`] = `52006`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick above 1`] = `29643`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick below 1`] = `29605`;

exports[`UniswapV3Pool gas tests fee is on #snapshotCumulativesInside tick inside 1`] = `37053`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block moves tick, no initialized crossings 1`] = `120257`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block with no tick movement 1`] = `104256`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing a single initialized tick 1`] = `135417`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap crossing several initialized ticks 1`] = `158693`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 first swap in block, large swap, no initialized crossings 1`] = `135162`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks after some time passes 1`] = `158693`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 large swap crossing several initialized ticks second time after some time passes 1`] = `218693`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block moves tick, no initialized crossings 1`] = `120257`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block with no tick movement 1`] = `104367`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing a single initialized tick 1`] = `121853`;

exports[`UniswapV3Pool gas tests fee is on #swapExact0For1 second swap in block, large swap crossing several initialized ticks 1`] = `145107`;
