name: Mythx

on:
  workflow_dispatch:

jobs:
  mythx:
    name: Submit to Mythx
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Set up node
        uses: actions/setup-node@v1
        with:
          node-version: 12

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Install node dependencies
        run: yarn install --frozen-lockfile

      - name: Install pip3
        run: |
          python -m pip install --upgrade pip

      - name: Install mythx CLI
        run: |
          pip3 install mythx-cli

      - name: Install solc-select
        run: |
          pip3 install solc-select

      - name: Install solc 0.8.12
        run: |
          solc-select install 0.8.12
          solc-select use 0.8.12

      - name: Submit code to Mythx
        run: |
          mythx --api-key ${{ secrets.MYTHX_API_KEY }} \
            --yes \
            analyze \
            --mode deep \
            --async \
            --create-group \
            --group-name "@uniswap/v3-core@${{ github.sha }}" \
            --solc-version 0.8.12 \
            --check-properties \
            contracts/test/TickBitmapEchidnaTest.sol --include TickBitmapEchidnaTest \
            contracts/test/TickMathEchidnaTest.sol --include TickMathEchidnaTest \
            contracts/test/SqrtPriceMathEchidnaTest.sol --include SqrtPriceMathEchidnaTest \
            contracts/test/SwapMathEchidnaTest.sol --include SwapMathEchidnaTest \
            contracts/test/TickEchidnaTest.sol --include TickEchidnaTest \
            contracts/test/TickOverflowSafetyEchidnaTest.sol --include TickOverflowSafetyEchidnaTest \
            contracts/test/OracleEchidnaTest.sol --include OracleEchidnaTest \
            contracts/test/BitMathEchidnaTest.sol --include BitMathEchidnaTest \
            contracts/test/UnsafeMathEchidnaTest.sol --include UnsafeMathEchidnaTest \
            contracts/test/FullMathEchidnaTest.sol --include FullMathEchidnaTest
