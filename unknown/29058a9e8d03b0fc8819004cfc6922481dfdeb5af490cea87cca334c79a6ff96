/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

// SPDX-License-Identifier: GPL-3.0
pragma solidity >=0.6.0 <0.9.0;

/// @title IMorphoBlueFlashLoanCallback Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface IMorphoBlueFlashLoanCallback {
    function onMorphoFlashLoan(uint256 _assets, bytes calldata _data) external;
}
