name: Tests
on: [push, pull_request]

jobs:
  check:
    name: Foundry project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: recursive

      - name: Install Foundry
        uses: onbjerg/foundry-toolchain@v1
        with:
          version: nightly

      - name: Install dependencies
        run: forge install
      - name: Run tests
        run: forge test -vvv
      - name: Build Test with older solc versions
        run: |
          forge build --contracts src/Test.sol --use solc:0.8.0
          forge build --contracts src/Test.sol --use solc:0.7.6
          forge build --contracts src/Test.sol --use solc:0.7.0
          forge build --contracts src/Test.sol --use solc:0.6.0
