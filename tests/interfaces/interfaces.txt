#######################################################################################################################
# PERSISTENT
#######################################################################################################################

# Dispatcher
IDispatcher.sol: Dispatcher.abi.json
IMigrationHookHandler.sol: IMigrationHookHandler.abi.json

# Arbitrary value oracle
IArbitraryValueOracle.sol: IArbitraryValueOracle.abi.json
IManualValueOracleFactory.sol: ManualValueOracleFactory.abi.json
IManualValueOracleLib.sol: ManualValueOracleLib.abi.json

# Off-chain
IHelperDataReader.sol: HelperDataReader.abi.json
IHelperDataReaderRouter.sol: HelperDataReaderRouter.abi.json
IFundDataProviderRouter.sol: FundDataProviderRouter.abi.json
IFundValueCalculatorRouter.sol: FundValueCalculatorRouter.abi.json
IFundValueCalculatorUsdWrapper.sol: FundValueCalculatorUsdWrapper.abi.json

# Address list registry
IAddressListRegistry.sol: AddressListRegistry.abi.json
IAaveV2ATokenListOwner.sol: AaveV2ATokenListOwner.abi.json
IAaveV3ATokenListOwner.sol: AaveV3ATokenListOwner.abi.json
ICompoundV3CTokenListOwner.sol: CompoundV3CTokenListOwner.abi.json

# Uint list registry
IUintListRegistry.sol: UintListRegistry.abi.json

# Global config
IGlobalConfigLib.sol: GlobalConfigLib.abi.json

# Shares splitter
ISharesSplitterLib.sol: SharesSplitterLib.abi.json
ISharesSplitterFactory.sol: SharesSplitterFactory.abi.json

# Protocol fee
IProtocolFeeReserveLib.sol: ProtocolFeeReserveLib.abi.json

# Shares wrapper
IGatedRedemptionQueueSharesWrapperLib.sol: GatedRedemptionQueueSharesWrapperLib.abi.json
IGatedRedemptionQueueSharesWrapperFactory.sol: GatedRedemptionQueueSharesWrapperFactory.abi.json

# External positions
IExternalPosition.sol: IExternalPosition.abi.json
IExternalPositionFactory.sol: ExternalPositionFactory.abi.json
IExternalPositionProxy.sol: ExternalPositionProxy.abi.json
IExternalPositionVault.sol: IExternalPositionVault.abi.json

# Peripheral
INonStandardPrecisionSimulatedAggregator.sol: NonStandardPrecisionSimulatedAggregator.abi.json
ISingleAssetDepositQueueLib.sol: SingleAssetDepositQueueLib.abi.json
ISingleAssetRedemptionQueueFactory.sol: SingleAssetRedemptionQueueFactory.abi.json
ISingleAssetRedemptionQueueLib.sol: SingleAssetRedemptionQueueLib.abi.json

# Smart accounts
IAaveV3FlashLoanAssetManager.sol: AaveV3FlashLoanAssetManagerLib.abi.json
IMorphoBlueFlashLoanAssetManager.sol: MorphoBlueFlashLoanAssetManagerLib.abi.json
IMultiCallAccountMixin.sol: MultiCallAccountMixin.abi.json
IMultiCallAccountMixinHarness.sol: MultiCallAccountMixinHarness.abi.json
ISharePriceThrottledAssetManagerLib.sol: SharePriceThrottledAssetManagerLib.abi.json
ISharePriceThrottledAssetManagerFactory.sol: SharePriceThrottledAssetManagerFactory.abi.json

# Utils
IAggregatorRateDeviationBaseHarness.sol: AggregatorRateDeviationBaseHarness.abi.json
IDispatcherOwnedBeaconFactory.sol: DispatcherOwnedBeaconFactory.abi.json
IGSNRecipientMixinHarness.sol: GSNRecipientMixinHarness.abi.json
IPriceFeedHelpersLibHarness.sol: PriceFeedHelpersLibHarness.abi.json
IRateAggregatorBaseHarness.sol: RateAggregatorBaseHarness.abi.json

#######################################################################################################################
# RELEASE
#######################################################################################################################

# Core
IVaultLib.sol: VaultLib.abi.json
IVaultCore.sol: IVaultCore.abi.json
IComptrollerLib.sol: ComptrollerLib.abi.json
IFundDeployer.sol: FundDeployer.abi.json
IExtension.sol: IExtension.abi.json

# Policies
IPolicy.sol: IPolicy.abi.json
IPolicyManager.sol: PolicyManager.abi.json
IAllowedAdapterIncomingAssetsPolicy.sol: AllowedAdapterIncomingAssetsPolicy.abi.json
IAllowedAdaptersPerManagerPolicy.sol: AllowedAdaptersPerManagerPolicy.abi.json
IAllowedAdaptersPolicy.sol: AllowedAdaptersPolicy.abi.json
IAllowedAssetsForRedemptionPolicy.sol: AllowedAssetsForRedemptionPolicy.abi.json
IAllowedDepositRecipientsPolicy.sol: AllowedDepositRecipientsPolicy.abi.json
IAllowedExternalPositionTypesPerManagerPolicy.sol: AllowedExternalPositionTypesPerManagerPolicy.abi.json
IAllowedExternalPositionTypesPolicy.sol: AllowedExternalPositionTypesPolicy.abi.json
IAllowedRedeemersForSpecificAssetsPolicy.sol: AllowedRedeemersForSpecificAssetsPolicy.abi.json
IAllowedSharesTransferRecipientsPolicy.sol: AllowedSharesTransferRecipientsPolicy.abi.json
ICumulativeSlippageTolerancePolicy.sol: CumulativeSlippageTolerancePolicy.abi.json
IMinAssetBalancesPostRedemptionPolicy.sol: MinAssetBalancesPostRedemptionPolicy.abi.json
IMinMaxInvestmentPolicy.sol: MinMaxInvestmentPolicy.abi.json
INoDepegOnRedeemSharesForSpecificAssetsPolicy.sol: NoDepegOnRedeemSharesForSpecificAssetsPolicy.abi.json
IOnlyRemoveDustExternalPositionPolicy.sol: OnlyRemoveDustExternalPositionPolicy.abi.json
IOnlyUntrackDustOrPricelessAssetsPolicy.sol: OnlyUntrackDustOrPricelessAssetsPolicy.abi.json

# Fees
IFee.sol: IFee.abi.json
IFeeManager.sol: FeeManager.abi.json
IManagementFee.sol: ManagementFee.abi.json
IMinSharesSupplyFee.sol: MinSharesSupplyFee.abi.json
IExitRateDirectFee.sol: ExitRateDirectFee.abi.json
IExitRateBurnFee.sol: ExitRateBurnFee.abi.json
IEntranceRateBurnFee.sol: EntranceRateBurnFee.abi.json
IEntranceRateDirectFee.sol: EntranceRateDirectFee.abi.json
IPerformanceFee.sol: PerformanceFee.abi.json

# Integrations
IIntegrationAdapter.sol: IIntegrationAdapter.abi.json
IIntegrationManager.sol: IntegrationManager.abi.json
IUniswapV2LiquidityAdapter.sol: UniswapV2LiquidityAdapter.abi.json
IAaveV3Adapter.sol: AaveV3Adapter.abi.json
IUniswapV3Adapter.sol: UniswapV3Adapter.abi.json
ICurveLiquidityAdapter.sol: CurveLiquidityAdapter.abi.json
IUniswapV2ExchangeAdapter.sol: UniswapV2ExchangeAdapter.abi.json
ICompoundV3Adapter.sol: CompoundV3Adapter.abi.json
IEnzymeV4VaultAdapter.sol: EnzymeV4VaultAdapter.abi.json
IYearnVaultV2Adapter.sol: YearnVaultV2Adapter.abi.json
IParaSwapV5Adapter.sol: ParaSwapV5Adapter.abi.json
IParaSwapV6Adapter.sol: ParaSwapV6Adapter.abi.json
IAaveV2Adapter.sol: AaveV2Adapter.abi.json
IBalancerV2LiquidityAdapter.sol: BalancerV2LiquidityAdapter.abi.json
ICompoundAdapter.sol: CompoundAdapter.abi.json
IERC4626Adapter.sol: ERC4626Adapter.abi.json
IZeroExV4Adapter.sol: ZeroExV4Adapter.abi.json
ISwellStakingAdapter.sol: SwellStakingAdapter.abi.json
IDivaStakingAdapter.sol: DivaStakingAdapter.abi.json
IGenericWrappingAdapterBase.sol: GenericWrappingAdapterBase.abi.json
IOneInchV5Adapter.sol: OneInchV5Adapter.abi.json
ITransferAssetsAdapter.sol: TransferAssetsAdapter.abi.json
IPendleV2Adapter.sol: PendleV2Adapter.abi.json

# External positions
IExternalPositionManager.sol: ExternalPositionManager.abi.json
IAaveDebtPositionLib.sol: AaveDebtPositionLib.abi.json
IAaveDebtPositionParser.sol: AaveDebtPositionParser.abi.json
IAaveV3DebtPositionLib.sol: AaveV3DebtPositionLib.abi.json
IAaveV3DebtPositionParser.sol: AaveV3DebtPositionParser.abi.json
IArbitraryLoanPositionLib.sol: ArbitraryLoanPositionLib.abi.json
IArbitraryLoanPositionParser.sol: ArbitraryLoanPositionParser.abi.json
IArbitraryLoanTotalNominalDeltaOracleModule.sol: ArbitraryLoanTotalNominalDeltaOracleModule.abi.json
ICompoundDebtPositionLib.sol: CompoundDebtPositionLib.abi.json
ICompoundDebtPositionParser.sol: CompoundDebtPositionParser.abi.json
IConvexVotingPositionLib.sol: ConvexVotingPositionLib.abi.json
IConvexVotingPositionParser.sol: ConvexVotingPositionParser.abi.json
IGMXV2LeverageTradingPositionLib.sol: GMXV2LeverageTradingPositionLib.abi.json
IGMXV2LeverageTradingPositionParser.sol: GMXV2LeverageTradingPositionParser.abi.json
IKilnStakingPositionLib.sol: KilnStakingPositionLib.abi.json
IKilnStakingPositionParser.sol: KilnStakingPositionParser.abi.json
ILidoWithdrawalsPositionLib.sol:LidoWithdrawalsPositionLib.abi.json
IMapleLiquidityPositionLib.sol: MapleLiquidityPositionLib.abi.json
IMapleLiquidityPositionParser.sol: MapleLiquidityPositionParser.abi.json
IStaderWithdrawalsPositionLib.sol:StaderWithdrawalsPositionLib.abi.json
IStakeWiseV3StakingPositionLib.sol: StakeWiseV3StakingPositionLib.abi.json
IStakeWiseV3StakingPositionParser.sol: StakeWiseV3StakingPositionParser.abi.json
ITheGraphDelegationPositionLib.sol: TheGraphDelegationPositionLib.abi.json
ITheGraphDelegationPositionParser.sol: TheGraphDelegationPositionParser.abi.json
IUniswapV3LiquidityPositionLib.sol: UniswapV3LiquidityPositionLib.abi.json
IUniswapV3LiquidityPositionParser.sol: UniswapV3LiquidityPositionParser.abi.json
ITermFinanceV1LendingPositionLib.sol: TermFinanceV1LendingPositionLib.abi.json
ITermFinanceV1LendingPositionParser.sol: TermFinanceV1LendingPositionParser.abi.json
IMorphoBluePositionLib.sol: MorphoBluePositionLib.abi.json
IMorphoBluePositionParser.sol: MorphoBluePositionParser.abi.json
IAlicePositionLib.sol: AlicePositionLib.abi.json
IAlicePositionParser.sol: AlicePositionParser.abi.json
IMysoV3OptionWritingPositionLib.sol: MysoV3OptionWritingPositionLib.abi.json
IMysoV3OptionWritingPositionParser.sol: MysoV3OptionWritingPositionParser.abi.json

# Value interpreter
IValueInterpreter.sol: ValueInterpreter.abi.json

# Price feeds
IDerivativePriceFeed.sol: IDerivativePriceFeed.abi.json
ICurvePriceFeed.sol: CurvePriceFeed.abi.json
IBalancerV2WeightedPoolPriceFeed.sol: BalancerV2WeightedPoolPriceFeed.abi.json
IBalancerV2StablePoolPriceFeed.sol: BalancerV2StablePoolPriceFeed.abi.json
ICompoundPriceFeed.sol: CompoundPriceFeed.abi.json
IUniswapV2PoolPriceFeed.sol: UniswapV2PoolPriceFeed.abi.json
IBalancerV2GaugeTokenPriceFeed.sol: BalancerV2GaugeTokenPriceFeed.abi.json
IRevertingPriceFeed.sol: RevertingPriceFeed.abi.json
IYearnVaultV2PriceFeed.sol: YearnVaultV2PriceFeed.abi.json
IChainlinkPriceFeedMixin.sol: ChainlinkPriceFeedMixin.abi.json
IERC4626PriceFeed.sol: ERC4626PriceFeed.abi.json
IEtherFiEthPriceFeed.sol: EtherFiEthPriceFeed.abi.json
IUsdEthSimulatedAggregator.sol: UsdEthSimulatedAggregator.abi.json
IPeggedDerivativesPriceFeed.sol: PeggedDerivativesPriceFeed.abi.json
IStaderSDPriceFeed.sol: StaderSDPriceFeed.abi.json
IConvertedQuoteAggregator.sol: ConvertedQuoteAggregator.abi.json
IEnzymeVaultPriceFeed.sol: EnzymeVaultPriceFeed.abi.json

# Protocol fee
IProtocolFeeTracker.sol: ProtocolFeeTracker.abi.json

# Gas relayer
IGasRelayPaymasterLib.sol: GasRelayPaymasterLib.abi.json
IGasRelayPaymasterFactory.sol: GasRelayPaymasterFactory.abi.json

# Peripheral
IDepositWrapper.sol: DepositWrapper.abi.json
IUnpermissionedActionsWrapper.sol: UnpermissionedActionsWrapper.abi.json

# Off-chain
IFundValueCalculator.sol: IFundValueCalculator.abi.json
IAssetValueCalculator.sol: AssetValueCalculator.abi.json
