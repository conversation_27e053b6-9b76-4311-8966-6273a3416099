/*
    This file is part of the Enzyme Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

// SPDX-License-Identifier: GPL-3.0
pragma solidity >=0.6.0 <0.9.0;

/// @title IAliceWhitelistManager Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface IAliceWhitelistManager {
    function addAddress(address _userAddress) external;

    function owner() external returns (address owner_);
}
