// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

abstract contract Constants {
    // Time
    uint256 internal constant SECONDS_ONE_MINUTE = 60;
    uint256 internal constant SECONDS_ONE_HOUR = SECONDS_ONE_MINUTE * 60;
    uint256 internal constant SECONDS_ONE_DAY = SECONDS_ONE_HOUR * 24;
    uint256 internal constant SECONDS_ONE_YEAR = SECONDS_ONE_DAY * 36525 / 100;

    // Percentages
    uint256 internal constant BPS_ONE_HUNDRED_PERCENT = 10_000;
    uint256 internal constant BPS_ONE_PERCENT = BPS_ONE_HUNDRED_PERCENT / 100;

    uint256 internal constant WEI_ONE_HUNDRED_PERCENT = 10 ** 18;
    uint256 internal constant WEI_ONE_PERCENT = WEI_ONE_HUNDRED_PERCENT / 100;

    // Network ChainIDs
    uint256 internal constant ETHEREUM_CHAIN_ID = 1;
    uint256 internal constant POLYGON_CHAIN_ID = 137;
    uint256 internal constant ARBITRUM_CHAIN_ID = 42161;
    uint256 internal constant BASE_CHAIN_ID = 8453;

    // Miscellaneous
    uint8 internal constant CHAINLINK_AGGREGATOR_DECIMALS_ETH = 18;
    uint8 internal constant CHAINLINK_AGGREGATOR_DECIMALS_USD = 8;
    uint256 internal constant CHAINLINK_AGGREGATOR_PRECISION_ETH = 10 ** CHAINLINK_AGGREGATOR_DECIMALS_ETH;
    uint256 internal constant CHAINLINK_AGGREGATOR_PRECISION_USD = 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD;

    // Network blocks (for fork tests)
    // Some tests may require specific blocks to guarantee a required setup,
    // expected exchange rates, etc.
    // `ETHEREUM_BLOCK_LATEST` can be increased as-needed, and should be used in all tests
    // that should generally continue to pass regardless of block.
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_STAKEWISE = 22400000; // May 3rd, 2025
    uint256 internal constant ETHEREUM_BLOCK_LATEST = 21710000; // Jan 26th, 2025
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE = 21710000; // Jan 26th, 2025
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_MYSO_V3 = 21679809; // Jan 22nd, 2025
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_PARASWAP_V6 = 21819120; // Feb 10th 2025
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_ONE_INCH_V5 = 19518890; // March 26th, 2024
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_PENDLE = 20100000; // June 15th, 2024
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_TERM_FINANCE = 18554000; // Nov 12th, 2023
    uint256 internal constant ETHEREUM_BLOCK_TIME_SENSITIVE_THE_GRAPH = 20711624; // Sep 9th, 2024

    uint256 internal constant POLYGON_BLOCK_LATEST = 67047280; // Jan 23rd, 2025
    uint256 internal constant POLYGON_BLOCK_TIME_SENSITIVE = 54900000; // March 21st, 2024
    uint256 internal constant POLYGON_BLOCK_TIME_SENSITIVE_ONE_INCH_V5 = 55136740; // March 27th, 2024

    uint256 internal constant ARBITRUM_BLOCK_LATEST = 278101140; // Nov 25th, 2024
    uint256 internal constant ARBITRUM_BLOCK_TIME_SENSITIVE = 231099000; // July 11th, 2024

    uint256 internal constant BASE_BLOCK_LATEST = 27583610; // March 14th, 2025
    uint256 internal constant BASE_CHAIN_BLOCK_TIME_SENSITIVE_ONE_INCH_V5 = 23218719; // Dec 3rd, 2024

    // Network assets
    address internal constant NATIVE_ASSET_ADDRESS = ******************************************;

    address internal constant ETHEREUM_AURA = ******************************************;
    address internal constant ETHEREUM_BAL = ******************************************;
    address internal constant ETHEREUM_CBBTC = ******************************************;
    address internal constant ETHEREUM_COMP = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V2_CDAI = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V2_CETH = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V2_CUSDC = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V2_CWBTC = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V3_CUSDC = ******************************************;
    address internal constant ETHEREUM_COMPOUND_V3_CWETH = ******************************************;
    address internal constant ETHEREUM_CRV = ******************************************;
    address internal constant ETHEREUM_CVX = ******************************************;
    address internal constant ETHEREUM_DAI = ******************************************;
    address internal constant ETHEREUM_EBTC = ******************************************;
    address internal constant ETHEREUM_EETH = ******************************************;
    address internal constant ETHEREUM_ETHERFI_LIQUIDITY_POOL = ******************************************;
    address internal constant ETHEREUM_ETH_X = ******************************************;
    address internal constant ETHEREUM_LBTC = ******************************************;
    address internal constant ETHEREUM_LDO = ******************************************;
    address internal constant ETHEREUM_LINK = ******************************************;
    address internal constant ETHEREUM_LUSD = ******************************************;
    address internal constant ETHEREUM_MBTC = ******************************************;
    address internal constant ETHEREUM_MLN = ******************************************;
    address internal constant ETHEREUM_PAXG = ******************************************;
    address internal constant ETHEREUM_STETH = ******************************************;
    address internal constant ETHEREUM_STKAAVE = ******************************************;
    address internal constant ETHEREUM_USDC = ******************************************;
    address internal constant ETHEREUM_USDE = ******************************************;
    address internal constant ETHEREUM_USDS = ******************************************;
    address internal constant ETHEREUM_USDT = ******************************************;
    address internal constant ETHEREUM_WETH = ******************************************;
    address internal constant ETHEREUM_WBTC = ******************************************;
    address internal constant ETHEREUM_WSTETH = ******************************************;

    address internal constant POLYGON_BAL = ******************************************;
    address internal constant POLYGON_COMPOUND_V3_CUSDC = ******************************************;
    address internal constant POLYGON_CRV = ******************************************;
    address internal constant POLYGON_DAI = ******************************************;
    address internal constant POLYGON_MATIC_X = ******************************************;
    address internal constant POLYGON_LINK = ******************************************;
    address internal constant POLYGON_MLN = ******************************************;
    address internal constant POLYGON_USDC = ******************************************;
    address internal constant POLYGON_USDT = ******************************************;
    address internal constant POLYGON_WBTC = ******************************************;
    address internal constant POLYGON_WETH = ******************************************;
    address internal constant POLYGON_WMATIC = ******************************************;

    address internal constant ARBITRUM_BAL = ******************************************;
    address internal constant ARBITRUM_CRV = ******************************************;
    address internal constant ARBITRUM_DAI = ******************************************;
    address internal constant ARBITRUM_EETH = ******************************************;
    address internal constant ARBITRUM_LINK = ******************************************;
    address internal constant ARBITRUM_MLN = ******************************************;
    address internal constant ARBITRUM_USDC = ******************************************;
    address internal constant ARBITRUM_USDT = ******************************************;
    address internal constant ARBITRUM_WBTC = ******************************************;
    address internal constant ARBITRUM_WETH = ******************************************;

    address internal constant BASE_CBETH = ******************************************;
    address internal constant BASE_DAI = ******************************************;
    address internal constant BASE_MLN = ******************************************;
    address internal constant BASE_USDC = ******************************************;
    address internal constant BASE_WETH = ******************************************;
    address internal constant BASE_WSTETH = ******************************************;

    // Network Chainlink aggregators
    address internal constant ETHEREUM_BAL_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_DAI_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_ETH_USD_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_MLN_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_STETH_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_USDC_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_USDT_ETH_AGGREGATOR = ******************************************;
    address internal constant ETHEREUM_WEETH_ETH_AGGREGATOR = ******************************************;

    address internal constant POLYGON_ETH_USD_AGGREGATOR = ******************************************;
    address internal constant POLYGON_DAI_ETH_AGGREGATOR = ******************************************;
    address internal constant POLYGON_MATIC_USD_AGGREGATOR = ******************************************;
    address internal constant POLYGON_MLN_ETH_AGGREGATOR = ******************************************;
    address internal constant POLYGON_USDC_USD_AGGREGATOR = ******************************************;
    address internal constant POLYGON_USDT_ETH_AGGREGATOR = ******************************************;
    address internal constant POLYGON_WBTC_USD_AGGREGATOR = ******************************************;

    address internal constant ARBITRUM_ETH_USD_AGGREGATOR = ******************************************;
    address internal constant ARBITRUM_BAL_USD_AGGREGATOR = ******************************************;
    address internal constant ARBITRUM_CRV_USD_AGGREGATOR = ******************************************;
    address internal constant ARBITRUM_DAI_USD_AGGREGATOR = ******************************************;
    // TODO: Replace with actual MLN/ETH aggregator.
    address internal constant ARBITRUM_MLN_ETH_AGGREGATOR = ******************************************;
    address internal constant ARBITRUM_USDC_USD_AGGREGATOR = ******************************************;
    address internal constant ARBITRUM_USDT_USD_AGGREGATOR = ******************************************;

    address internal constant BASE_ETH_USD_AGGREGATOR = ******************************************;
    address internal constant BASE_MLN_ETH_AGGREGATOR = ******************************************; // TODO: add this
    address internal constant BASE_USDC_USD_AGGREGATOR = ******************************************;
    address internal constant BASE_WSTETH_ETH_AGGREGATOR = ******************************************;

    // Network External contracts
    address internal constant ETHEREUM_MERKL_DISTRIBUTOR = ******************************************;

    address internal constant POLYGON_MERKL_DISTRIBUTOR = ETHEREUM_MERKL_DISTRIBUTOR;

    address internal constant ARBITRUM_MERKL_DISTRIBUTOR = ETHEREUM_MERKL_DISTRIBUTOR;

    address internal constant BASE_MERKL_DISTRIBUTOR = ETHEREUM_MERKL_DISTRIBUTOR;
}
