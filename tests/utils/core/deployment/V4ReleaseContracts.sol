// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IExternalPositionManager} from "tests/interfaces/internal/v4/IExternalPositionManager.sol";
import {IFeeManager} from "tests/interfaces/internal/v4/IFeeManager.sol";
import {IFundDeployer} from "tests/interfaces/internal/v4/IFundDeployer.sol";
import {IGasRelayPaymasterFactory} from "tests/interfaces/internal/v4/IGasRelayPaymasterFactory.sol";
import {IIntegrationManager} from "tests/interfaces/internal/v4/IIntegrationManager.sol";
import {IPolicyManager} from "tests/interfaces/internal/v4/IPolicyManager.sol";
import {IProtocolFeeTracker} from "tests/interfaces/internal/v4/IProtocolFeeTracker.sol";
import {IUsdEthSimulatedAggregator} from "tests/interfaces/internal/v4/IUsdEthSimulatedAggregator.sol";
import {IValueInterpreter} from "tests/interfaces/internal/v4/IValueInterpreter.sol";

struct Contracts {
    // Core
    address comptrollerLibAddress;
    IFundDeployer fundDeployer;
    address vaultLibAddress;
    // Extensions
    IExternalPositionManager externalPositionManager;
    IFeeManager feeManager;
    IIntegrationManager integrationManager;
    IPolicyManager policyManager;
    // Infrastructure
    IGasRelayPaymasterFactory gasRelayPaymasterFactory;
    IProtocolFeeTracker protocolFeeTracker;
    IValueInterpreter valueInterpreter;
    IUsdEthSimulatedAggregator usdEthSimulatedAggregator;
}

function getMainnetDeployment() pure returns (Contracts memory) {
    return Contracts({
        // Core
        comptrollerLibAddress: ******************************************,
        fundDeployer: IFundDeployer(******************************************),
        vaultLibAddress: ******************************************,
        // Extensions
        externalPositionManager: IExternalPositionManager(******************************************),
        feeManager: IFeeManager(******************************************),
        integrationManager: IIntegrationManager(******************************************),
        policyManager: IPolicyManager(******************************************),
        // Infrastructure
        gasRelayPaymasterFactory: IGasRelayPaymasterFactory(******************************************),
        protocolFeeTracker: IProtocolFeeTracker(******************************************),
        valueInterpreter: IValueInterpreter(******************************************),
        usdEthSimulatedAggregator: IUsdEthSimulatedAggregator(******************************************)
    });
}

function getPolygonDeployment() pure returns (Contracts memory) {
    return Contracts({
        // Core
        comptrollerLibAddress: ******************************************,
        fundDeployer: IFundDeployer(******************************************),
        vaultLibAddress: ******************************************,
        // Extensions
        externalPositionManager: IExternalPositionManager(******************************************),
        feeManager: IFeeManager(******************************************),
        integrationManager: IIntegrationManager(******************************************),
        policyManager: IPolicyManager(******************************************),
        // Infrastructure
        gasRelayPaymasterFactory: IGasRelayPaymasterFactory(******************************************),
        protocolFeeTracker: IProtocolFeeTracker(******************************************),
        valueInterpreter: IValueInterpreter(******************************************),
        usdEthSimulatedAggregator: IUsdEthSimulatedAggregator(******************************************)
    });
}

function getArbitrumDeployment() pure returns (Contracts memory contracts_) {
    return Contracts({
        // Core
        comptrollerLibAddress: ******************************************,
        fundDeployer: IFundDeployer(******************************************),
        vaultLibAddress: ******************************************,
        // Extensions
        externalPositionManager: IExternalPositionManager(******************************************),
        feeManager: IFeeManager(******************************************),
        integrationManager: IIntegrationManager(******************************************),
        policyManager: IPolicyManager(******************************************),
        // Infrastructure
        gasRelayPaymasterFactory: IGasRelayPaymasterFactory(******************************************),
        protocolFeeTracker: IProtocolFeeTracker(******************************************),
        valueInterpreter: IValueInterpreter(******************************************),
        usdEthSimulatedAggregator: IUsdEthSimulatedAggregator(address(0)) // Not deployed on Arbitrum
    });
}

function getBaseChainDeployment() pure returns (Contracts memory contracts_) {
    return Contracts({
        // Core
        comptrollerLibAddress: ******************************************,
        fundDeployer: IFundDeployer(******************************************),
        vaultLibAddress: ******************************************,
        // Extensions
        externalPositionManager: IExternalPositionManager(******************************************),
        feeManager: IFeeManager(******************************************),
        integrationManager: IIntegrationManager(******************************************),
        policyManager: IPolicyManager(******************************************),
        // Infrastructure
        gasRelayPaymasterFactory: IGasRelayPaymasterFactory(******************************************),
        protocolFeeTracker: IProtocolFeeTracker(******************************************),
        valueInterpreter: IValueInterpreter(******************************************),
        usdEthSimulatedAggregator: IUsdEthSimulatedAggregator(address(0)) // Not deployed on Base
    });
}
