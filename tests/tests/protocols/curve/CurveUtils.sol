// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {AddOnUtilsBase} from "tests/utils/bases/AddOnUtilsBase.sol";

import {ICurvePriceFeed} from "tests/interfaces/internal/ICurvePriceFeed.sol";
import {IFundDeployer} from "tests/interfaces/internal/IFundDeployer.sol";

abstract contract CurveUtils is AddOnUtilsBase {
    address internal constant ADDRESS_PROVIDER_ADDRESS = ******************************************;

    address internal constant ETHEREUM_GAUGE_CONTROLLER_ADDRESS = ******************************************;
    // ETHEREUM_GAUGE_CONTROLLER_ADMIN can change and can also be queried from ETHEREUM_GAUGE_CONTROLLER.admin()
    address internal constant ETHEREUM_GAUGE_CONTROLLER_ADMIN_ADDRESS = ******************************************;
    address internal constant ETHEREUM_MINTER_ADDRESS = ******************************************;
    address internal constant ETHEREUM_POOL_OWNER_ADDRESS = ******************************************;
    address internal constant POLYGON_POOL_OWNER_ADDRESS = ******************************************;
    address internal constant ARBITRUM_POOL_OWNER_ADDRESS = ******************************************;

    // Pools: Ethereum
    // underlyings (aave-style)
    address internal constant ETHEREUM_AAVE_POOL_ADDRESS = ******************************************;
    address internal constant ETHEREUM_AAVE_POOL_GAUGE_TOKEN_ADDRESS = ******************************************;
    address internal constant ETHEREUM_AAVE_POOL_LP_TOKEN_ADDRESS = ******************************************;
    // native asset
    address internal constant ETHEREUM_STETH_NG_POOL_ADDRESS = ******************************************;
    address internal constant ETHEREUM_STETH_NG_POOL_GAUGE_TOKEN_ADDRESS = ******************************************;
    address internal constant ETHEREUM_STETH_NG_POOL_LP_TOKEN_ADDRESS = ******************************************;
    // metapool
    address internal constant ETHEREUM_META_POOL_ADDRESS = ******************************************;
    address internal constant ETHEREUM_META_POOL_GAUGE_TOKEN_ADDRESS = ******************************************;
    address internal constant ETHEREUM_META_POOL_LP_TOKEN_ADDRESS = ******************************************;
    // basepool
    address internal constant ETHEREUM_BASE_POOL_ADDRESS = ******************************************;
    address internal constant ETHEREUM_BASE_POOL_GAUGE_TOKEN_ADDRESS = ******************************************;
    address internal constant ETHEREUM_BASE_POOL_LP_TOKEN_ADDRESS = ******************************************;

    // Pools: Polygon
    // underlyings (aave-style)
    address internal constant POLYGON_AAVE_POOL_ADDRESS = ******************************************;
    address internal constant POLYGON_AAVE_POOL_GAUGE_TOKEN_ADDRESS = ******************************************;
    address internal constant POLYGON_AAVE_POOL_LP_TOKEN_ADDRESS = ******************************************;
    // metapool
    address internal constant POLYGON_META_POOL_ADDRESS = ******************************************;
    address internal constant POLYGON_META_POOL_LP_TOKEN_ADDRESS = ******************************************;
    // TODO: native asset

    // Pools: Arbitrum
    address internal constant ARBITRUM_2POOL_ADDRESS = ******************************************;
    address internal constant ARBITRUM_2POOL_LP_TOKEN_ADDRESS = ******************************************;

    function deployPriceFeed(
        IFundDeployer _fundDeployer,
        address _addressProviderAddress,
        address _poolOwnerAddress,
        uint256 _virtualPriceDeviationThreshold
    ) internal returns (ICurvePriceFeed priceFeed_) {
        bytes memory args =
            abi.encode(_fundDeployer, _addressProviderAddress, _poolOwnerAddress, _virtualPriceDeviationThreshold);

        return ICurvePriceFeed(deployCode("CurvePriceFeed.sol", args));
    }
}
