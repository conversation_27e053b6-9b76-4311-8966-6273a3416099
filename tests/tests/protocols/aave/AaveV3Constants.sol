// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

// Ethereum
address constant ETHEREUM_POOL_ADDRESS = ******************************************;
address constant ETHEREUM_POOL_ADDRESS_PROVIDER = ******************************************;
address constant ETHEREUM_PROTOCOL_DATA_PROVIDER = ******************************************;
address constant ETHEREUM_REWARDS_CONTROLLER = ******************************************;

// Polygon
address constant POLYGON_POOL_ADDRESS = ******************************************;
address constant POLYGON_POOL_ADDRESS_PROVIDER = ******************************************;
address constant POLYGON_PROTOCOL_DATA_PROVIDER = ******************************************;
address constant POLYGON_REWARDS_CONTROLLER = ******************************************;

// Arbitrum
address constant ARBITRUM_POOL_ADDRESS = ******************************************;
address constant ARBITRUM_POOL_ADDRESS_PROVIDER = ******************************************;
address constant ARBITRUM_PROTOCOL_DATA_PROVIDER = ******************************************;
address constant ARBITRUM_REWARDS_CONTROLLER = ******************************************;

// Base
address constant BASE_POOL_ADDRESS = ******************************************;
address constant BASE_POOL_ADDRESS_PROVIDER = 0xe20fCBdBfFC4Dd138cE8b2E6FBb6CB49777ad64D;
address constant BASE_PROTOCOL_DATA_PROVIDER = 0xd82a47fdebB5bf5329b09441C3DaB4b5df2153Ad;
address constant BASE_REWARDS_CONTROLLER = 0xf9cc4F0D883F1a1eb2c253bdb46c254Ca51E1F44;
