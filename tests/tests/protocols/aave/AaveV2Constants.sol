// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

// Ethereum
address constant ETHEREUM_LENDING_POOL_ADDRESS = ******************************************;
address constant ETHEREUM_LENDING_POOL_ADDRESS_PROVIDER_ADDRESS = ******************************************;
address constant ETHEREUM_INCENTIVES_CONTROLLER = ******************************************;
address constant ETHEREUM_PROTOCOL_DATA_PROVIDER = ******************************************;

// Polygon
address constant POLYGON_LENDING_POOL_ADDRESS = ******************************************;
address constant POLYGON_LENDING_POOL_ADDRESS_PROVIDER_ADDRESS = ******************************************;
address constant POLYGON_INCENTIVES_CONTROLLER = ******************************************;
address constant POLYGON_PROTOCOL_DATA_PROVIDER = ******************************************;
