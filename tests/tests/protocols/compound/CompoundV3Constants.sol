// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

// Ethereum
address constant ETHEREUM_COMPOUND_V3_CONFIGURATOR = ******************************************;
address constant ETHEREUM_COMPOUND_V3_REWARDS = ******************************************;

// Polygon
address constant POLYGON_COMPOUND_V3_CONFIGURATOR = ******************************************;
address constant POLYGON_COMPOUND_V3_REWARDS = ******************************************;
