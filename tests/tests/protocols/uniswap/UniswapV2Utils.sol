    // SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IUniswapV2Pair} from "tests/interfaces/external/IUniswapV2Pair.sol";

address constant ETHEREUM_UNISWAP_V2_POOL_WETH_USDC = ******************************************;
address constant ETHEREUM_UNISWAP_V2_FACTORY = ******************************************;
address constant ETHEREUM_UNISWAP_V2_ROUTER = ******************************************;

address constant POLYGON_UNISWAP_V2_POOL_WMATIC_USDT = ******************************************;
address constant POLYGON_UNISWAP_V2_FACTORY = ******************************************;
address constant POLYGON_UNISWAP_V2_ROUTER = ******************************************;

address constant ARBITRUM_UNISWAP_V2_POOL_WETH_USDC = ******************************************;
address constant ARBITRUM_UNISWAP_V2_FACTORY = ******************************************;
address constant ARBITRUM_UNISWAP_V2_ROUTER = ******************************************;

abstract contract UniswapV2Utils {
    function getExpectedUnderlyingTokenAmounts(address _poolTokenAddress, uint256 _redeemPoolTokenAmount)
        internal
        view
        returns (uint256 expectedToken0Amount_, uint256 expectedToken1Amount_)
    {
        uint256 poolTokensSupply = IUniswapV2Pair(_poolTokenAddress).totalSupply();
        IERC20 token0 = IERC20(IUniswapV2Pair(_poolTokenAddress).token0());
        IERC20 token1 = IERC20(IUniswapV2Pair(_poolTokenAddress).token1());
        uint256 poolToken0Balance = token0.balanceOf(_poolTokenAddress);
        uint256 poolToken1Balance = token1.balanceOf(_poolTokenAddress);

        expectedToken0Amount_ = _redeemPoolTokenAmount * poolToken0Balance / poolTokensSupply;
        expectedToken1Amount_ = _redeemPoolTokenAmount * poolToken1Balance / poolTokensSupply;

        return (expectedToken0Amount_, expectedToken1Amount_);
    }
}
