// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

address constant ETHEREUM_ZERO_LEND_RWA_STABLECOINS_AAVE_V3_POOL = ******************************************;
address constant ETHEREUM_ZERO_LEND_RWA_STABLECOINS_AAVE_V3_POOL_ADDRESS_PROVIDER =
    ******************************************;
address constant ETHEREUM_ZERO_LEND_RWA_STABLECOINS_AAVE_V3_PROTOCOL_DATA_PROVIDER =
    ******************************************;
address constant ETHEREUM_ZERO_LEND_RWA_STABLECOINS_AAVE_V3_REWARDS_CONTROLLER =
    ******************************************;

address constant ETHEREUM_ZERO_LEND_LRT_BTC_AAVE_V3_POOL = ******************************************;
address constant ETHEREUM_ZERO_LEND_LRT_BTC_AAVE_V3_POOL_ADDRESS_PROVIDER = ******************************************;
address constant ETHEREUM_ZERO_LEND_LRT_BTC_AAVE_V3_PROTOCOL_DATA_PROVIDER = ******************************************;
address constant ETHEREUM_ZERO_LEND_LRT_BTC_AAVE_V3_REWARDS_CONTROLLER = ******************************************;
