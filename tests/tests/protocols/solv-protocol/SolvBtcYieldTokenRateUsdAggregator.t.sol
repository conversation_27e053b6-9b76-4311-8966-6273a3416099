// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IChainlinkAggregator} from "tests/interfaces/external/IChainlinkAggregator.sol";

address constant SOLV_BTC_BBN = ******************************************;
address constant SOLV_BTC_IN_USD_AGGREGATOR = ******************************************;

contract Test is IntegrationTest {
    IChainlinkAggregator yieldTokenAggregator;

    function setUp() public override {
        vm.createSelectFork("mainnet", ETHEREUM_BLOCK_TIME_SENSITIVE);

        yieldTokenAggregator = __deployYieldTokenAggregator({_solvBtcYieldTokenAddress: SOLV_BTC_BBN});
    }

    // DEPLOYMENT HELPERS

    function __deployYieldTokenAggregator(address _solvBtcYieldTokenAddress)
        private
        returns (IChainlinkAggregator aggregator_)
    {
        bytes memory args = abi.encode(SOLV_BTC_IN_USD_AGGREGATOR, _solvBtcYieldTokenAddress);

        return IChainlinkAggregator(deployCode("SolvBtcYieldTokenRateUsdAggregator.sol", args));
    }

    // TESTS

    function test_decimals_success() public {
        assertEq(yieldTokenAggregator.decimals(), CHAINLINK_AGGREGATOR_DECIMALS_USD, "Incorrect decimals");
    }

    function test_latestRoundData_success() public {
        (uint256 rate,) = parseRateFromChainlinkAggregator(address(yieldTokenAggregator));

        // Yield-bearing token, so price should gradually grow from 1 BTC
        // BTC price on Jan 26, 2025: $102-105k
        assertEq(rate, 10465349056256, "Incorrect rate");
    }
}
