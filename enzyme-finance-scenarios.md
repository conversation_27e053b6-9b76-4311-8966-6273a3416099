# Основные сценарии использования протокола Enzyme Finance

В этом документе описаны основные сценарии использования протокола Enzyme Finance, включая детальный поток вызовов функций, основных акторов и их взаимодействие с контрактами. Также отмечены функции, входящие в scope баг-баунти программы.

## Сценарий 1: Создание фонда

### Акторы
- **Менеджер фонда**: создает и настраивает фонд
- **FundDeployer**: контракт, отвечающий за развертывание новых фондов
- **Dispatcher**: контракт, управляющий VaultProxy

### Поток вызовов

1. Менеджер фонда вызывает `FundDeployer.createNewFund()`
   ```
   FundDeployer.createNewFund(
     string _fundName,
     string _fundSymbol,
     address _denominationAsset,
     uint256 _sharesActionTimelock,
     bytes _feeManagerConfigData,
     bytes _policyManagerConfigData,
     address _fundOwner
   )
   ```

2. `FundDeployer` создает `ComptrollerProxy`
   ```
   ComptrollerProxy = new ComptrollerProxy(...)
   ```

3. `FundDeployer` запрашивает `Dispatcher` для создания `VaultProxy`
   ```
   Dispatcher.deployVaultProxy(
     address _vaultLib,
     address _owner,
     address _fundDeployer,
     address _comptrollerProxy,
     string _fundName,
     string _fundSymbol
   )
   ```

4. `FundDeployer` настраивает расширения
   ```
   FeeManager.setConfigForFund(_feeManagerConfigData)
   PolicyManager.setConfigForFund(_policyManagerConfigData)
   ```

5. `FundDeployer` активирует фонд
   ```
   ComptrollerProxy.activate(address _denominationAsset)
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `Dispatcher` - НАЙДЕН в scope.md (строки 130, 390, 616, 744)
- ✅ `FundDeployer` - НАЙДЕН в scope.md (строки 108, 406, 632, 760)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `FeeManager` - НАЙДЕН в scope.md (строки 110, 404, 630, 758)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)

## Сценарий 2: Депозит в фонд

### Акторы
- **Инвестор**: вносит средства в фонд
- **ComptrollerProxy**: обрабатывает запрос на покупку долей
- **VaultProxy**: хранит активы и выпускает доли

### Поток вызовов

#### Прямой депозит

1. Инвестор вызывает `ComptrollerProxy.buyShares()`
   ```
   ComptrollerProxy.buyShares(
     address _buyer,
     uint256 _investmentAmount,
     uint256 _minSharesQuantity
   )
   ```

2. `ComptrollerProxy` проверяет политики через `PolicyManager`
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook _hook,
     bytes _validationData
   )
   ```

3. `ComptrollerProxy` передает активы в `VaultProxy`
   ```
   IERC20(_denominationAsset).transferFrom(_buyer, _vaultProxy, _investmentAmount)
   ```

4. `ComptrollerProxy` вызывает `FeeManager` для обработки входных комиссий
   ```
   FeeManager.invokeHook(
     IPolicyManager.PolicyHook.BuySharesSetup,
     _buyer,
     _investmentAmount
   )
   ```

5. `VaultProxy` выпускает доли инвестору
   ```
   VaultProxy.mintShares(_buyer, _sharesQuantity)
   ```

#### Депозит через DepositWrapper

1. Инвестор вызывает `DepositWrapper.depositForFund()`
   ```
   DepositWrapper.depositForFund(
     address _comptrollerProxy,
     address _buyer,
     address _inputAsset,
     uint256 _inputAmount,
     address _denominationAsset,
     uint256 _minSharesQuantity,
     bool _exchangeInputToUnderlying
   )
   ```

2. Если необходимо, `DepositWrapper` обменивает входящий актив на деноминацию фонда
   ```
   // Через интеграцию с DEX
   ```

3. `DepositWrapper` вызывает `ComptrollerProxy.buyShares()`
   ```
   ComptrollerProxy.buyShares(
     _buyer,
     _denominationAssetAmount,
     _minSharesQuantity
   )
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)
- ✅ `FeeManager` - НАЙДЕН в scope.md (строки 110, 404, 630, 758)
- ✅ `DepositWrapper` - НАЙДЕН в scope.md (строки 134, 386, 614, 742)
- ✅ `EntranceRateBurnFee` - НАЙДЕН в scope.md (строки 128, 392, 618, 746)
- ✅ `EntranceRateDirectFee` - НАЙДЕН в scope.md (строки 126, 394, 620, 748)
- ✅ `MinMaxInvestmentPolicy` - НАЙДЕН в scope.md (строки 88, 430, 652, 528)
- ✅ `AllowedDepositRecipientsPolicy` - НАЙДЕН в scope.md (строки 150, 364, 582, 720)

## Сценарий 3: Торговля активами

### Акторы
- **Менеджер фонда**: инициирует торговые операции
- **IntegrationManager**: управляет взаимодействием с внешними протоколами
- **Адаптеры**: обеспечивают интерфейс к внешним протоколам

### Поток вызовов

1. Менеджер фонда вызывает `ComptrollerProxy.callOnExtension()`
   ```
   ComptrollerProxy.callOnExtension(
     address _extension,
     uint256 _actionId,
     bytes _callArgs
   )
   ```

2. `ComptrollerProxy` перенаправляет вызов в `IntegrationManager`
   ```
   IntegrationManager.receiveCallFromComptroller(
     address _comptrollerProxy,
     uint256 _actionId,
     bytes _callArgs
   )
   ```

3. `IntegrationManager` декодирует аргументы и вызывает адаптер
   ```
   IntegrationManager.callOnIntegration(
     address _adapter,
     bytes _encodedCallArgs
   )
   ```

4. `IntegrationManager` проверяет политики через `PolicyManager`
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PreCallOnIntegration,
     _validationData
   )
   ```

5. Адаптер (например, `ParaSwapV6Adapter`) подготавливает данные для действия
   ```
   ParaSwapV6Adapter.parseAssetsForAction(
     address _vaultProxy,
     bytes4 _selector,
     bytes _actionData
   )
   ```

6. `IntegrationManager` выполняет действие, передавая активы
   ```
   VaultProxy.transferAssets(
     _spendAssets,
     _spendAssetAmounts,
     _adapter,
     _extraAssetsToTransfer,
     _extraAssetAmounts
   )
   ```

7. Адаптер взаимодействует с внешним протоколом (например, ParaSwap)
   ```
   // Вызов внешнего протокола для обмена
   ```

8. `IntegrationManager` проверяет результат операции
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PostCallOnIntegration,
     _validationData
   )
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `IntegrationManager` - НАЙДЕН в scope.md (строки 94, 424, 646, 522)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `ParaSwapV5Adapter` - НАЙДЕН в scope.md (строки 72, 442, 660, 536)
- ✅ `ParaSwapV6Adapter` - НАЙДЕН в scope.md (строки 70, 180, 190, 196)
- ✅ `OneInchV5Adapter` - НАЙДЕН в scope.md (строки 78, 282, 330, 436)
- ✅ `CurveLiquidityAdapter` - НАЙДЕН в scope.md (строки 610, 738)
- ✅ `TransferAssetsAdapter` - НАЙДЕН в scope.md (строки 48, 238, 246, 252)
- ✅ `CumulativeSlippageTolerancePolicy` - НАЙДЕН в scope.md (строки 136, 384, 608, 736)
- ✅ `AllowedAdaptersPolicy` - НАЙДЕН в scope.md (строки 154, 360, 578, 716)
- ✅ `AllowedAdaptersPerManagerPolicy` - НАЙДЕН в scope.md (строки 156, 358, 576, 714)
- ✅ `AllowedAdapterIncomingAssetsPolicy` - НАЙДЕН в scope.md (строки 158, 356, 574, 712)

## Сценарий 4: Управление внешними позициями

### Акторы
- **Менеджер фонда**: управляет внешними позициями
- **ExternalPositionManager**: координирует действия с внешними позициями
- **Внешние позиции**: специализированные контракты для взаимодействия с протоколами

### Поток вызовов

#### Создание внешней позиции

1. Менеджер фонда вызывает `ComptrollerProxy.callOnExtension()`
   ```
   ComptrollerProxy.callOnExtension(
     address _extension, // ExternalPositionManager
     uint256 _actionId, // CREATE_EXTERNAL_POSITION
     bytes _callArgs
   )
   ```

2. `ExternalPositionManager` создает новую внешнюю позицию
   ```
   ExternalPositionManager.createExternalPosition(
     address _comptrollerProxy,
     uint256 _typeId,
     bytes _initializationData
   )
   ```

3. `ExternalPositionManager` использует `ExternalPositionFactory` для создания позиции
   ```
   ExternalPositionFactory.create(
     address _vaultProxy,
     uint256 _typeId,
     bytes _initializationData
   )
   ```

4. `ExternalPositionManager` проверяет политики
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.CreateExternalPosition,
     _validationData
   )
   ```

#### Действия с внешней позицией

1. Менеджер фонда вызывает `ComptrollerProxy.callOnExtension()`
   ```
   ComptrollerProxy.callOnExtension(
     address _extension, // ExternalPositionManager
     uint256 _actionId, // CALL_ON_EXTERNAL_POSITION
     bytes _callArgs
   )
   ```

2. `ExternalPositionManager` перенаправляет вызов на внешнюю позицию
   ```
   ExternalPositionManager.callOnExternalPosition(
     address _comptrollerProxy,
     uint256 _externalPositionId,
     uint256 _actionId,
     bytes _actionArgs
   )
   ```

3. `ExternalPositionManager` проверяет политики
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PreCallOnExternalPosition,
     _validationData
   )
   ```

4. Внешняя позиция (например, `MorphoBluePositionLib`) выполняет действие
   ```
   MorphoBluePositionLib.receiveCallFromVault(
     uint256 _actionId,
     bytes _actionArgs
   )
   ```

5. `ExternalPositionManager` проверяет результат операции
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PostCallOnExternalPosition,
     _validationData
   )
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `ExternalPositionManager` - НАЙДЕН в scope.md (строки 112, 402, 628, 756)
- ✅ `ExternalPositionFactory` - НАЙДЕН в scope.md (строки 114, 400, 626, 754)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `MorphoBluePositionLib` - НАЙДЕН в scope.md (строки 84, 230)
- ✅ `MorphoBluePositionParser` - НАЙДЕН в scope.md (строки 82, 228)
- ✅ `AllowedExternalPositionTypesPolicy` - НАЙДЕН в scope.md (строки 146, 368, 586, 724)
- ✅ `AllowedExternalPositionTypesPerManagerPolicy` - НАЙДЕН в scope.md (строки 148, 366, 584, 722)
- ✅ `OnlyRemoveDustExternalPositionPolicy` - НАЙДЕН в scope.md (строки 76, 438, 656, 532)

## Сценарий 5: Выкуп долей фонда

### Акторы
- **Инвестор**: выкупает свои доли фонда
- **ComptrollerProxy**: обрабатывает запрос на выкуп
- **VaultProxy**: хранит активы и сжигает доли

### Поток вызовов

#### Выкуп всеми активами пропорционально

1. Инвестор вызывает `ComptrollerProxy.redeemShares()`
   ```
   ComptrollerProxy.redeemShares(
     address _redeemer,
     uint256 _sharesQuantity,
     address _recipient,
     uint256 _minTotalAmountOut
   )
   ```

2. `ComptrollerProxy` проверяет политики через `PolicyManager`
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PreRedeemShares,
     _validationData
   )
   ```

3. `ComptrollerProxy` вызывает `FeeManager` для обработки выходных комиссий
   ```
   FeeManager.invokeHook(
     IPolicyManager.PolicyHook.RedeemSharesSetup,
     _redeemer,
     _sharesQuantity
   )
   ```

4. `VaultProxy` сжигает доли инвестора
   ```
   VaultProxy.burnShares(_redeemer, _sharesQuantity)
   ```

5. `VaultProxy` передает активы инвестору пропорционально
   ```
   // Для каждого актива в фонде
   VaultProxy.transferAsset(
     _asset,
     _recipient,
     _assetAmount
   )
   ```

#### Выкуп конкретными активами

1. Инвестор вызывает `ComptrollerProxy.redeemSharesForSpecificAssets()`
   ```
   ComptrollerProxy.redeemSharesForSpecificAssets(
     address _redeemer,
     uint256 _sharesQuantity,
     address[] _payoutAssets,
     uint256[] _payoutAssetPercentages,
     address _recipient
   )
   ```

2. `ComptrollerProxy` проверяет политики
   ```
   PolicyManager.validatePolicies(
     address _comptrollerProxy,
     IPolicyManager.PolicyHook.PreRedeemSharesForSpecificAssets,
     _validationData
   )
   ```

3. `ComptrollerProxy` вызывает `FeeManager` для обработки выходных комиссий
   ```
   FeeManager.invokeHook(
     IPolicyManager.PolicyHook.RedeemSharesSetup,
     _redeemer,
     _sharesQuantity
   )
   ```

4. `VaultProxy` сжигает доли инвестора
   ```
   VaultProxy.burnShares(_redeemer, _sharesQuantity)
   ```

5. `VaultProxy` передает указанные активы инвестору
   ```
   // Для каждого указанного актива
   VaultProxy.transferAsset(
     _payoutAssets[i],
     _recipient,
     _payoutAmounts[i]
   )
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)
- ✅ `FeeManager` - НАЙДЕН в scope.md (строки 110, 404, 630, 758)
- ✅ `ExitRateBurnFee` - НАЙДЕН в scope.md (строки 118, 396, 622, 750)
- ✅ `ExitRateDirectFee` - НАЙДЕН в scope.md (строки 116, 398, 624, 752)
- ✅ `AllowedAssetsForRedemptionPolicy` - НАЙДЕН в scope.md (строки 152, 362, 580, 718)
- ✅ `MinAssetBalancesPostRedemptionPolicy` - НАЙДЕН в scope.md (строки 90, 428, 650, 526)
- ✅ `NoDepegOnRedeemSharesForSpecificAssetsPolicy` - НАЙДЕН в scope.md (строки 80, 286, 328, 434)
- ✅ `SingleAssetRedemptionQueueFactory` - НАЙДЕН в scope.md (строки 52, 200, 272, 338)
- ✅ `SingleAssetRedemptionQueueLib` - НАЙДЕН в scope.md (строки 50, 202, 274, 340)

## Сценарий 6: Расчет стоимости фонда

### Акторы
- **Любой пользователь**: запрашивает информацию о стоимости фонда
- **FundValueCalculator**: рассчитывает стоимость активов фонда
- **ValueInterpreter**: интерпретирует стоимость активов

### Поток вызовов

1. Пользователь вызывает `FundValueCalculatorRouter.calcGav()`
   ```
   FundValueCalculatorRouter.calcGav(
     address _vaultProxy,
     bool _requireFinality
   )
   ```

2. `FundValueCalculatorRouter` перенаправляет вызов на соответствующий `FundValueCalculator`
   ```
   FundValueCalculator.calcGav(
     address _vaultProxy,
     bool _requireFinality
   )
   ```

3. `FundValueCalculator` получает список активов фонда
   ```
   VaultProxy.getTrackedAssets()
   ```

4. Для каждого актива `FundValueCalculator` запрашивает его стоимость через `ValueInterpreter`
   ```
   ValueInterpreter.calcCanonicalAssetValue(
     address _baseAsset,
     uint256 _baseAssetAmount,
     address _quoteAsset
   )
   ```

5. `ValueInterpreter` использует соответствующий `PriceFeed` для получения цены
   ```
   // Например, для Curve LP токена
   CurvePriceFeed.calcUnderlyingValues(
     address _derivative,
     uint256 _derivativeAmount
   )
   ```

6. Для внешних позиций `FundValueCalculator` запрашивает их стоимость
   ```
   ExternalPositionManager.getExternalPositionValue(
     address _externalPosition,
     address _denominationAsset
   )
   ```

7. `FundValueCalculator` суммирует стоимости всех активов и внешних позиций

### Контракты в scope баг-баунти
- ✅ `FundValueCalculator` - НАЙДЕН в scope.md (строки 106, 408, 634, 762)
- ✅ `FundValueCalculatorRouter` - НАЙДЕН в scope.md (строки 104, 410, 636, 764)
- ✅ `ValueInterpreter` - НАЙДЕН в scope.md (строки 42, 460, 696, 562)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `ExternalPositionManager` - НАЙДЕН в scope.md (строки 112, 402, 628, 756)
- ✅ `CurvePriceFeed` - НАЙДЕН в scope.md (строки 612, 740)
- ✅ `ERC4626PriceFeed` - НАЙДЕН в scope.md (строки 122, 320, 322, 478)
- ✅ `YearnVaultV2PriceFeed` - НАЙДЕН в scope.md (строка 702)
- ✅ `ERC4626RateAggregatorFactory` - НАЙДЕН в scope.md (строки 120, 182, 192, 198)
- ✅ `PeggedRateDeviationAggregatorFactory` - НАЙДЕН в scope.md (строки 68, 178, 188, 194)
- ✅ `ConvertedQuoteAggregatorFactory` - НАЙДЕН в scope.md (строки 138, 204, 206, 208)

## Сценарий 7: Реконфигурация фонда

### Акторы
- **Владелец фонда**: инициирует процесс реконфигурации
- **FundDeployer**: управляет процессом реконфигурации
- **Migrator**: выполняет миграцию между конфигурациями
- **ComptrollerProxy**: старый и новый контроллеры фонда

### Поток вызовов

#### Создание запроса на реконфигурацию

1. Владелец фонда вызывает `FundDeployer.createReconfigurationRequest()`
   ```
   FundDeployer.createReconfigurationRequest(
     address _vaultProxy,
     IComptroller.ConfigInput _comptrollerConfig,
     uint256 _executableTimestamp
   )
   ```

2. `FundDeployer` проверяет права доступа
   ```
   require(
     IVault(_vaultProxy).getOwner() == msg.sender,
     "createReconfigurationRequest: Only the VaultProxy owner can call this function"
   )
   ```

3. `FundDeployer` создает новый `ComptrollerProxy` для новой конфигурации
   ```
   address nextComptrollerProxy = __deployComptrollerProxy();
   ```

4. `FundDeployer` инициализирует новый контроллер с новой конфигурацией
   ```
   __initializeComptrollerProxy({
     _canonicalSender: msg.sender,
     _comptrollerProxy: nextComptrollerProxy,
     _vaultProxy: _vaultProxy,
     _comptrollerConfig: _comptrollerConfig
   });
   ```

5. `FundDeployer` сохраняет запрос на реконфигурацию
   ```
   vaultProxyToReconfigurationRequest[_vaultProxy] = ReconfigurationRequest({
     nextComptrollerProxy: nextComptrollerProxy,
     executableTimestamp: _executableTimestamp
   });
   ```

#### Выполнение реконфигурации

1. Migrator вызывает `FundDeployer.executeReconfiguration()`
   ```
   FundDeployer.executeReconfiguration(address _vaultProxy)
   ```

2. `FundDeployer` проверяет валидность запроса и временные ограничения
   ```
   require(
     request.nextComptrollerProxy != address(0),
     "executeReconfiguration: No reconfiguration request exists"
   );
   require(
     block.timestamp >= request.executableTimestamp,
     "executeReconfiguration: The reconfiguration timelock has not elapsed"
   );
   ```

3. `FundDeployer` получает текущий контроллер и выполняет деструктивные действия
   ```
   address prevComptrollerProxy = IVault(_vaultProxy).getAccessor();
   IComptroller(prevComptrollerProxy).destructActivated();
   ```

4. `FundDeployer` переключает VaultProxy на новый контроллер через Dispatcher
   ```
   IDispatcher(getDispatcher()).setCurrentFundDeployer(address(this));
   IVault(_vaultProxy).setAccessor(request.nextComptrollerProxy);
   ```

5. `FundDeployer` активирует новый контроллер
   ```
   IComptroller(request.nextComptrollerProxy).activate();
   ```

6. `FundDeployer` обновляет маппинги и очищает запрос
   ```
   comptrollerProxyToVaultProxy[request.nextComptrollerProxy] = _vaultProxy;
   delete vaultProxyToReconfigurationRequest[_vaultProxy];
   ```

#### Отмена реконфигурации

1. Владелец фонда может отменить запрос до его выполнения
   ```
   FundDeployer.cancelReconfiguration(address _vaultProxy)
   ```

2. `FundDeployer` проверяет права и существование запроса
   ```
   require(
     IVault(_vaultProxy).getOwner() == msg.sender,
     "cancelReconfiguration: Only the VaultProxy owner can call this function"
   );
   ```

3. `FundDeployer` деструктивно отключает новый контроллер
   ```
   IComptroller(nextComptrollerProxy).destructUnactivated();
   ```

4. `FundDeployer` очищает запрос на реконфигурацию
   ```
   delete vaultProxyToReconfigurationRequest[_vaultProxy];
   ```

### Контракты в scope баг-баунти
- ✅ `FundDeployer` - НАЙДЕН в scope.md (строки 108, 406, 632, 760)
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `Dispatcher` - НАЙДЕН в scope.md (строки 130, 390, 616, 744)
- ✅ `FeeManager` - НАЙДЕН в scope.md (строки 110, 404, 630, 758)
- ✅ `PolicyManager` - НАЙДЕН в scope.md (строки 64, 446, 664, 540)

## Сценарий 8: Начисление и выплата комиссий

### Акторы
- **Менеджер фонда**: инициирует начисление комиссий
- **FeeManager**: координирует процесс начисления комиссий
- **Комиссии**: конкретные реализации комиссий (ManagementFee, PerformanceFee и др.)

### Поток вызовов

#### Периодическое начисление комиссий

1. Менеджер фонда вызывает `ComptrollerProxy.callOnExtension()`
   ```
   ComptrollerProxy.callOnExtension(
     address _extension, // FeeManager
     uint256 _actionId, // INVOKE_HOOK
     bytes _callArgs
   )
   ```

2. `ComptrollerProxy` перенаправляет вызов в `FeeManager`
   ```
   FeeManager.receiveCallFromComptroller(
     address _comptrollerProxy,
     uint256 _actionId,
     bytes _callArgs
   )
   ```

3. `FeeManager` вызывает хук для всех зарегистрированных комиссий
   ```
   FeeManager.invokeHook(
     IFeeManager.FeeHook _hook,
     address _target,
     bytes _settlementData
   )
   ```

4. Для каждой комиссии `FeeManager` вызывает методы обновления и расчета
   ```
   // Например, для ManagementFee
   ManagementFee.update(
     address _comptrollerProxy,
     bytes _settlementData
   )
   ```

5. `FeeManager` рассчитывает размер комиссии и выпускает доли
   ```
   VaultProxy.mintShares(_feeRecipient, _sharesQuantity)
   ```

### Контракты в scope баг-баунти
- ✅ `ComptrollerLib` - НАЙДЕН в scope.md (строки 140, 382, 482, 734)
- ✅ `FeeManager` - НАЙДЕН в scope.md (строки 110, 404, 630, 758)
- ✅ `VaultLib` - НАЙДЕН в scope.md (строки 40, 462, 698, 774)
- ✅ `ManagementFee` - НАЙДЕН в scope.md (строки 92, 426, 648, 524)
- ✅ `PerformanceFee` - НАЙДЕН в scope.md (строки 66, 444, 662, 538)

---

## Сводка по пересечениям контрактов

### Статистика покрытия scope-контрактов в сценариях:

**Всего уникальных контрактов в scope.md:** ~150+ (с учетом дубликатов на разных сетях)
**Контрактов, упомянутых в сценариях:** 35
**Покрытие основных контрактов:** 100% (ВСЕ упомянутые контракты найдены в scope.md)

### Ключевые контракты, присутствующие во всех сценариях:
- `ComptrollerLib` - основной контроллер фонда
- `VaultLib` - хранилище активов фонда
- `FeeManager` - управление комиссиями
- `PolicyManager` - управление политиками
- `FundDeployer` - развертывание и управление фондами

### Контракты из scope.md, которые НЕ упоминаются в сценариях:
- AaveV3DebtPositionLib/Parser
- AaveV2Adapter/ATokenListOwner
- AddressListRegistry
- ArbitraryLoanPositionLib/Parser
- BalancerV2 адаптеры и price feeds
- CompoundV3 адаптеры
- GasRelayPaymaster контракты
- GlobalConfig контракты
- Kiln/Lido/Stader staking позиции
- UniswapV3 адаптеры
- UnpermissionedActionsWrapper
- UintListRegistry
- И многие другие специализированные адаптеры

### Исправления в анализе:
- ✅ `CurveLiquidityAdapter` - НАЙДЕН в scope.md (строки 610, 738)
- ✅ `CurvePriceFeed` - НАЙДЕН в scope.md (строки 612, 740)
- ✅ ВСЕ контракты из сценариев НАЙДЕНЫ в scope.md
- ✅ `PolicyManager`, `IntegrationManager`, `ManagementFee`, `PerformanceFee` - все найдены в scope.md

### Рекомендации:
1. Добавить сценарии для Aave V3 debt positions
2. Добавить сценарии для Balancer V2 интеграций
3. Добавить сценарии для staking позиций (Lido, Kiln, Stader)
4. Рассмотреть добавление сценариев для gas relay функциональности