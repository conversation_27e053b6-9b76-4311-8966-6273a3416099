# Scope баг-баунти программы Enzyme Finance

Ниже представлен список смарт-контрактов, входящих в scope баг-баунти программы, с их полными путями в репозитории:

- AaveDebtPositionLib - contracts/release/extensions/external-position-manager/external-positions/aave-debt/AaveDebtPositionLib.sol
- AaveDebtPositionParser - "contracts/release/extensions/external-position-manager/external-positions/aave-debt/AaveDebtPositionParser.sol"
- AavePriceFeed - `contracts/release/infrastructure/price-feeds/primitives/AavePriceFeed.sol`
- AddressListRegistry - `contracts/persistent/address-list-registry/AddressListRegistry.sol`
- AllowedAdapterIncomingAssetsPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdapterIncomingAssetsPolicy.sol`
- AllowedAdaptersPerManagerPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdaptersPerManagerPolicy.sol`
- AllowedAdaptersPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedAdaptersPolicy.sol`
- AllowedAssetsForRedemptionPolicy - `contracts/release/extensions/policy-manager/policies/redemption/AllowedAssetsForRedemptionPolicy.sol`
- AllowedDepositRecipientsPolicy - `contracts/release/extensions/policy-manager/policies/deposit/AllowedDepositRecipientsPolicy.sol`
- AllowedExternalPositionTypesPerManagerPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedExternalPositionTypesPerManagerPolicy.sol`
- AllowedExternalPositionTypesPolicy - `contracts/release/extensions/policy-manager/policies/utils/AllowedExternalPositionTypesPolicy.sol`
- AllowedSharesTransferRecipientsPolicy - `contracts/release/extensions/policy-manager/policies/shares/AllowedSharesTransferRecipientsPolicy.sol`
- ArbitraryLoanPositionLib - `contracts/release/extensions/external-position-manager/external-positions/arbitrary-loan/ArbitraryLoanPositionLib.sol`
- ArbitraryLoanPositionParser - `contracts/release/extensions/external-position-manager/external-positions/arbitrary-loan/ArbitraryLoanPositionParser.sol`
- ArbitraryLoanTotalNominalDeltaOracleModule - `contracts/release/extensions/external-position-manager/external-positions/arbitrary-loan/modules/ArbitraryLoanTotalNominalDeltaOracleModule.sol`
- ComptrollerLib - `contracts/release/core/ComptrollerLib.sol`
- CumulativeSlippageTolerancePolicy - `contracts/release/extensions/policy-manager/policies/trading/CumulativeSlippageTolerancePolicy.sol`
- CurveLiquidityAdapter - `contracts/release/extensions/integration-manager/integrations/adapters/CurveLiquidityAdapter.sol`
- CurvePriceFeed - `contracts/release/infrastructure/price-feeds/primitives/CurvePriceFeed.sol`
- DepositWrapper - `contracts/release/peripheral/DepositWrapper.sol`
- Dispatcher - `contracts/persistent/dispatcher/Dispatcher.sol`
- EntranceRateBurnFee - `contracts/release/extensions/fee-manager/fees/EntranceRateBurnFee.sol`
- EntranceRateDirectFee - `contracts/release/extensions/fee-manager/fees/EntranceRateDirectFee.sol`
- ExitRateBurnFee - `contracts/release/extensions/fee-manager/fees/ExitRateBurnFee.sol`
- ExitRateDirectFee - `contracts/release/extensions/fee-manager/fees/ExitRateDirectFee.sol`
- ExternalPositionFactory - `contracts/release/extensions/external-position-manager/ExternalPositionFactory.sol`
- ExternalPositionManager - `contracts/release/extensions/external-position-manager/ExternalPositionManager.sol`
- FeeManager - `contracts/release/extensions/fee-manager/FeeManager.sol`
- FundDeployer - `contracts/release/core/FundDeployer.sol`
- FundValueCalculator - `contracts/release/infrastructure/value-interpreter/FundValueCalculator.sol`
- FundValueCalculatorRouter - `contracts/release/infrastructure/value-interpreter/FundValueCalculatorRouter.sol`
- GasRelayPaymasterFactory - `contracts/persistent/gas-relayer/GasRelayPaymasterFactory.sol`
- GasRelayPaymasterLib - `contracts/persistent/gas-relayer/GasRelayPaymasterLib.sol`
- GlobalConfigLib - `contracts/persistent/global-config/GlobalConfigLib.sol`
- GlobalConfigProxy - `contracts/persistent/global-config/GlobalConfigProxy.sol`
- UniswapV3LiquidityPositionLib - `contracts/release/extensions/external-position-manager/external-positions/uniswap-v3-liquidity/UniswapV3LiquidityPositionLib.sol`
- UniswapV3LiquidityPositionParser - `contracts/release/extensions/external-position-manager/external-positions/uniswap-v3-liquidity/UniswapV3LiquidityPositionParser.sol`
- UnpermissionedActionsWrapper - `contracts/release/peripheral/UnpermissionedActionsWrapper.sol`
- UsdEthSimulatedAggregator - `contracts/release/infrastructure/price-feeds/primitives/UsdEthSimulatedAggregator.sol`
- ValueInterpreter - `contracts/release/infrastructure/value-interpreter/ValueInterpreter.sol`
- VaultLib - `contracts/release/core/VaultLib.sol`
- YearnVaultV2Adapter - `contracts/release/extensions/integration-manager/integrations/adapters/YearnVaultV2Adapter.sol`
- YearnVaultV2PriceFeed - `contracts/release/infrastructure/price-feeds/primitives/YearnVaultV2PriceFeed.sol`