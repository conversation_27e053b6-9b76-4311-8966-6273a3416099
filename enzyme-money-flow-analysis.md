# Enzyme Finance - Анализ денежных потоков (Money Flow Analysis)

## Общие принципы денежных потоков в Enzyme

### Базовые константы и формулы:
- `SHARES_UNIT = 10^18` - базовая единица для расчета долей
- `ONE_HUNDRED_PERCENT = 10000` - 100% в базисных пунктах
- `Share Price = GAV / Total Supply` - цена доли фонда
- `Shares Issued = Investment Amount * SHARES_UNIT / Share Price`

---

## СЦЕНАРИЙ 1: Создание фонда

### 💰 Денежные потоки
**Входящие потоки:** ОТСУТСТВУЮТ (только gas от создателя)
**Исходящие потоки:** ОТСУТСТВУЮТ
**Промежуточные операции:** Создание контрактов, настройка параметров

### 📊 Пример операции:
```
Начальное состояние:
- Менеджер фонда: 1000 ETH (для gas)
- Vault: не существует
- Total Supply: 0 shares

После создания:
- Менеджер фонда: 999.95 ETH (потрачено 0.05 ETH на gas)
- Vault: создан, баланс 0 USDC
- Total Supply: 0 shares
- Denomination Asset: USDC
```

### ⚠️ Точки риска:
- Некорректная настройка denomination asset
- Неправильная конфигурация комиссий/политик
- Отсутствие валидации параметров

### 🏦 Балансовые изменения:
- **FundDeployer**: Создает контракты (gas cost)
- **Dispatcher**: Регистрирует VaultProxy
- **ComptrollerLib**: Инициализируется с параметрами
- **VaultLib**: Создается пустой vault

---

## СЦЕНАРИЙ 2: Депозит в фонд

### 💰 Денежные потоки

#### 2A. Прямой депозит (1000 USDC → shares)

**Входящие потоки:**
- Инвестор → VaultProxy: 1000 USDC

**Исходящие потоки:**
- VaultProxy → Fee Recipient: 20 USDC (2% entrance fee)
- VaultProxy → Инвестор: 980 shares (эквивалент 980 USDC)

**Промежуточные операции:**
1. PolicyManager валидирует депозит
2. FeeManager рассчитывает entrance fee
3. VaultProxy принимает USDC
4. VaultProxy выпускает shares

### 📊 Пример операции:
```
Начальное состояние:
- Инвестор: 1000 USDC, 0 shares
- Vault: 0 USDC, Total Supply: 0 shares
- Share Price: 1.0 USDC (первый депозит)

Расчеты:
- Investment Amount: 1000 USDC
- Entrance Fee (2%): 1000 * 0.02 = 20 USDC
- Net Investment: 1000 - 20 = 980 USDC
- Shares Issued: 980 * 10^18 / 1.0 = 980 * 10^18 shares

После депозита:
- Инвестор: 0 USDC, 980 * 10^18 shares
- Vault: 980 USDC, Total Supply: 980 * 10^18 shares
- Fee Recipient: 20 USDC (или 20 * 10^18 fee shares)
```

#### 2B. Депозит через DepositWrapper (1000 DAI → USDC → shares)

**Входящие потоки:**
- Инвестор → DepositWrapper: 1000 DAI

**Исходящие потоки:**
- DepositWrapper → DEX: 1000 DAI
- DEX → DepositWrapper: 995 USDC (после slippage)
- DepositWrapper → VaultProxy: 995 USDC
- VaultProxy → Fee Recipient: 19.9 USDC (2% fee)
- VaultProxy → Инвестор: 975.1 shares

### 📊 Пример операции:
```
Начальное состояние:
- Инвестор: 1000 DAI, 0 shares
- Vault: 1000 USDC, Total Supply: 1000 * 10^18 shares
- Share Price: 1.0 USDC

Операции:
1. DEX обмен: 1000 DAI → 995 USDC (0.5% slippage)
2. Entrance Fee: 995 * 0.02 = 19.9 USDC
3. Net Investment: 995 - 19.9 = 975.1 USDC
4. Shares Issued: 975.1 * 10^18 / 1.0 = 975.1 * 10^18 shares

После депозита:
- Инвестор: 0 DAI, 975.1 * 10^18 shares
- Vault: 1975.1 USDC, Total Supply: 1975.1 * 10^18 shares
- Fee Recipient: 19.9 USDC
```

### ⚠️ Точки риска:
- **Slippage атаки** при обмене через DEX
- **Front-running** депозитов для манипуляции share price
- **Reentrancy** в процессе mint shares
- **Fee calculation overflow** при больших суммах
- **Price manipulation** через flash loans

### 🏦 Балансовые изменения:
- **Инвестор**: -1000 USDC, +980 shares
- **VaultProxy**: +980 USDC, +980 shares в total supply
- **Fee Recipient**: +20 USDC (или fee shares)
- **PolicyManager**: Проверки без изменения балансов
- **FeeManager**: Координация без изменения балансов

---

## СЦЕНАРИЙ 3: Торговля активами

### 💰 Денежные потоки

#### 3A. Обмен USDC → WETH через ParaSwap

**Входящие потоки:**
- VaultProxy → ParaSwapAdapter: 500 USDC

**Исходящие потоки:**
- ParaSwapAdapter → ParaSwap: 500 USDC
- ParaSwap → ParaSwapAdapter: 0.2 WETH
- ParaSwapAdapter → VaultProxy: 0.2 WETH

**Промежуточные операции:**
1. PolicyManager валидирует торговлю (pre-hook)
2. IntegrationManager координирует обмен
3. ParaSwapAdapter выполняет обмен
4. PolicyManager проверяет результат (post-hook)

### 📊 Пример операции:
```
Начальное состояние:
- Vault: 1000 USDC, 0 WETH
- GAV: 1000 USDC
- ETH Price: 2500 USDC

Торговая операция:
- Spend Asset: 500 USDC
- Expected Receive: 500 / 2500 = 0.2 WETH
- Actual Receive: 0.199 WETH (0.5% slippage)
- Slippage: (0.2 - 0.199) / 0.2 = 0.5%

После торговли:
- Vault: 500 USDC, 0.199 WETH
- GAV: 500 + (0.199 * 2500) = 997.5 USDC
- Потери от slippage: 2.5 USDC
```

### ⚠️ Точки риска:
- **Sandwich атаки** вокруг торговых операций
- **Slippage превышение** установленных лимитов
- **MEV атаки** через front-running
- **Adapter manipulation** - злонамеренные адаптеры
- **Price oracle manipulation** для GAV расчетов

### 🏦 Балансовые изменения:
- **VaultProxy**: -500 USDC, +0.199 WETH
- **ParaSwapAdapter**: Временно +500 USDC, -500 USDC, +0.2 WETH, -0.199 WETH
- **ParaSwap Protocol**: +500 USDC, -0.2 WETH (+ fees)
- **IntegrationManager**: Координация без изменения балансов

---

## СЦЕНАРИЙ 4: Управление внешними позициями

### 💰 Денежные потоки

#### 4A. Создание Morpho Blue позиции (Supply 1000 USDC)

**Входящие потоки:**
- VaultProxy → MorphoBluePosition: 1000 USDC

**Исходящие потоки:**
- MorphoBluePosition → Morpho Protocol: 1000 USDC
- Morpho Protocol → MorphoBluePosition: 1000 mUSDC (supply tokens)

**Промежуточные операции:**
1. ExternalPositionManager создает позицию
2. MorphoBluePositionLib выполняет supply
3. Позиция получает mUSDC токены
4. GAV обновляется с учетом внешней позиции

### 📊 Пример операции:
```
Начальное состояние:
- Vault: 2000 USDC, 0 external positions
- GAV: 2000 USDC

Создание позиции:
- Transfer to Position: 1000 USDC
- Morpho Supply: 1000 USDC → 1000 mUSDC
- Position Value: 1000 USDC (1:1 rate initially)

После создания позиции:
- Vault: 1000 USDC
- External Position: 1000 mUSDC (worth 1000 USDC)
- GAV: 1000 + 1000 = 2000 USDC (unchanged)
```

#### 4B. Получение yield от позиции (через время)

```
Через 30 дней:
- Morpho Position: 1000 mUSDC → 1005 USDC (0.5% yield)
- GAV: 1000 + 1005 = 2005 USDC
- Yield Generated: 5 USDC
```

### ⚠️ Точки риска:
- **Smart contract риски** внешних протоколов
- **Liquidation риски** в lending позициях
- **Impermanent loss** в LP позициях
- **Oracle failures** для оценки позиций
- **Withdrawal delays** в некоторых протоколах

### 🏦 Балансовые изменения:
- **VaultProxy**: -1000 USDC (transferred to position)
- **MorphoBluePosition**: +1000 mUSDC (managed assets)
- **ExternalPositionManager**: Tracking без изменения балансов
- **Morpho Protocol**: +1000 USDC liquidity

---

## СЦЕНАРИЙ 5: Выкуп долей фонда

### 💰 Денежные потоки

#### 5A. Пропорциональный выкуп (100 shares → assets)

**Входящие потоки:**
- Инвестор → VaultProxy: 100 shares (для сжигания)

**Исходящие потоки:**
- VaultProxy → Fee Recipient: 2 USDC (2% exit fee)
- VaultProxy → Инвестор: 98 USDC, 0.0196 WETH (пропорционально)

### 📊 Пример операции:
```
Начальное состояние:
- Vault: 1000 USDC, 0.2 WETH
- Total Supply: 1000 shares
- Инвестор: 100 shares
- GAV: 1000 + (0.2 * 2500) = 1500 USDC
- Share Price: 1500 / 1000 = 1.5 USDC

Выкуп расчеты:
- Shares to Redeem: 100 shares
- Redemption Value: 100 * 1.5 = 150 USDC
- Exit Fee (2%): 150 * 0.02 = 3 USDC
- Net Redemption: 150 - 3 = 147 USDC

Пропорциональное распределение:
- USDC portion: (1000 / 1500) * 147 = 98 USDC
- WETH portion: (500 / 1500) * 147 = 49 USDC worth = 0.0196 WETH

После выкупа:
- Инвестор: 0 shares, +98 USDC, +0.0196 WETH
- Vault: 902 USDC, 0.1804 WETH
- Total Supply: 900 shares
- Fee Recipient: +3 USDC
```

#### 5B. Выкуп конкретными активами (100 shares → только USDC)

```
Выкуп только в USDC:
- Redemption Value: 147 USDC (после fees)
- Requested: 100% USDC
- Need to convert: 0.0196 WETH → USDC
- WETH conversion: 0.0196 * 2500 = 49 USDC
- Total USDC to investor: 98 + 49 = 147 USDC

После выкупа:
- Инвестор: 0 shares, +147 USDC
- Vault: 853 USDC, 0.1804 WETH
- Total Supply: 900 shares
```

### ⚠️ Точки риска:
- **Liquidity crunches** при больших выкупах
- **Price impact** при конвертации активов
- **Run on the fund** - массовые выкупы
- **Asset depegging** при specific asset redemptions
- **Slippage** при конвертации в конкретные активы

### 🏦 Балансовые изменения:
- **Инвестор**: -100 shares, +98 USDC, +0.0196 WETH
- **VaultProxy**: -98 USDC, -0.0196 WETH, -100 shares from supply
- **Fee Recipient**: +3 USDC (или fee shares)

---

## СЦЕНАРИЙ 6: Расчет стоимости фонда

### 💰 Денежные потоки
**Входящие потоки:** ОТСУТСТВУЮТ (read-only операция)
**Исходящие потоки:** ОТСУТСТВУЮТ
**Промежуточные операции:** Агрегация цен, расчет стоимости позиций

### 📊 Пример расчета GAV:
```
Активы в Vault:
- 1000 USDC (tracked asset)
- 0.2 WETH (tracked asset)
- 100 CURVE-LP tokens (tracked asset)

Внешние позиции:
- Morpho Position: 1005 USDC value
- Uniswap V3 Position: 200 USDC value

Price Feed данные:
- USDC: $1.00
- WETH: $2500.00
- CURVE-LP: $1.50 per token

GAV Calculation:
- USDC value: 1000 * 1.00 = 1000 USDC
- WETH value: 0.2 * 2500 = 500 USDC
- CURVE-LP value: 100 * 1.50 = 150 USDC
- External positions: 1005 + 200 = 1205 USDC
- Total GAV: 1000 + 500 + 150 + 1205 = 2855 USDC
```

### ⚠️ Точки риска:
- **Oracle manipulation** для завышения GAV
- **Stale prices** в price feeds
- **Illiquid assets** неточная оценка
- **External position failures** неправильная оценка
- **Calculation overflow** при больших суммах

---

## СЦЕНАРИЙ 7: Начисление и выплата комиссий

### 💰 Денежные потоки

#### 7A. Management Fee (2% годовых, 30 дней)

**Входящие потоки:** ОТСУТСТВУЮТ (создание новых shares)
**Исходящие потоки:**
- VaultProxy → Fee Recipient: 16.44 shares (новые shares как комиссия)

### 📊 Пример расчета Management Fee:
```
Начальное состояние:
- GAV: 1000 USDC
- Total Supply: 1000 shares
- Management Fee: 2% годовых
- Время с последнего начисления: 30 дней

Расчет комиссии:
- Annual Rate: 2% = 0.02
- Daily Rate: 0.02 / 365 = 0.0000548
- 30-day Rate: 0.0000548 * 30 = 0.001644
- Fee Amount: 1000 * 0.001644 = 1.644 USDC
- Shares to Mint: 1.644 / 1.0 = 1.644 shares

Но используется compound formula:
- Scaled Rate: (1.02)^(1/365) = 1.0000548 (per day)
- 30-day Multiplier: (1.0000548)^30 = 1.001644
- Net Shares Supply: 1000 shares
- Shares Due: 1000 * (1.001644 - 1) = 1.644 shares

После начисления:
- Total Supply: 1001.644 shares
- Fee Recipient: +1.644 shares
- GAV: 1000 USDC (unchanged)
- Share Price: 1000 / 1001.644 = 0.9984 USDC
```

#### 7B. Performance Fee (20% от прибыли свыше HWM)

```
Начальное состояние:
- GAV: 1200 USDC (рост с 1000 USDC)
- Total Supply: 1000 shares
- High Water Mark: 1.0 USDC per share
- Performance Fee: 20%

Расчет комиссии:
- Current Share Price: 1200 / 1000 = 1.2 USDC
- Price Increase: 1.2 - 1.0 = 0.2 USDC per share
- Performance: 0.2 * 1000 = 200 USDC total
- Fee Amount: 200 * 0.20 = 40 USDC
- Shares to Mint: 40 / 1.2 = 33.33 shares

После начисления:
- Total Supply: 1033.33 shares
- Fee Recipient: +33.33 shares
- GAV: 1200 USDC (unchanged)
- New Share Price: 1200 / 1033.33 = 1.161 USDC
- New HWM: 1.161 USDC
```

### ⚠️ Точки риска:
- **Fee calculation errors** приводящие к переплате
- **High water mark manipulation** для снижения performance fees
- **Compound interest overflow** в long-term calculations
- **Timing attacks** для минимизации fees
- **Share dilution attacks** через fee manipulation

### 🏦 Балансовые изменения:
- **VaultProxy**: +новые shares в total supply
- **Fee Recipient**: +fee shares
- **GAV**: Остается неизменным
- **Share Price**: Снижается из-за dilution

---

## СВОДНЫЙ АНАЛИЗ РИСКОВ ПО ДЕНЕЖНЫМ ПОТОКАМ

### 🔴 Критические риски потери средств

#### 1. Reentrancy атаки
**Сценарии:** Депозит, Выкуп, Торговля
**Механизм:** Повторный вызов функций до завершения state updates
**Потенциальные потери:** До 100% средств vault
**Пример:**
```
1. Пользователь вызывает redeemShares(1000 shares)
2. В процессе transfer активов вызывается malicious contract
3. Malicious contract снова вызывает redeemShares(1000 shares)
4. Shares еще не сожжены, получает активы дважды
```

#### 2. Price Oracle Manipulation
**Сценарии:** Расчет GAV, Performance Fees, Торговля
**Механизм:** Манипуляция ценами через flash loans
**Потенциальные потери:** 10-50% GAV
**Пример:**
```
1. Flash loan 10M USDC
2. Купить WETH, поднять цену с $2500 до $3000
3. Вызвать calcGAV() - завышенная оценка
4. Депозит по завышенной цене shares
5. Вернуть flash loan, цена падает
6. Получить больше shares, чем должен
```

#### 3. Sandwich атаки при торговле
**Сценарии:** Торговля через адаптеры
**Механизм:** Front-run и back-run торговых операций
**Потенциальные потери:** 1-5% от суммы торговли
**Пример:**
```
1. Фонд хочет купить 100 WETH за 250K USDC
2. MEV bot видит транзакцию в mempool
3. Bot покупает WETH первым, поднимает цену
4. Фонд покупает по завышенной цене
5. Bot продает WETH, получает прибыль
```

### 🟡 Средние риски

#### 4. Slippage превышение
**Сценарии:** Торговля, Депозит через DepositWrapper
**Потенциальные потери:** 0.5-2% от суммы операции

#### 5. Fee calculation errors
**Сценарии:** Все операции с комиссиями
**Потенциальные потери:** Переплата комиссий до 10%

#### 6. Liquidity crunches
**Сценарии:** Массовые выкупы долей
**Потенциальные потери:** Forced selling по низким ценам

### 🟢 Низкие риски

#### 7. Rounding errors
**Потенциальные потери:** Wei-level amounts

#### 8. Gas price manipulation
**Потенциальные потери:** DoS атаки

---

## МАТЕМАТИЧЕСКИЕ ФОРМУЛЫ И КОНСТАНТЫ

### Базовые расчеты:
```solidity
// Цена доли
sharePrice = GAV / totalSupply

// Количество долей при депозите
sharesIssued = (investmentAmount - fees) * SHARES_UNIT / sharePrice

// Стоимость выкупа
redemptionValue = sharesToRedeem * sharePrice / SHARES_UNIT

// Management Fee (compound)
feeShares = netSharesSupply * (rpow(scaledPerSecondRate, secondsSinceSettlement) - RATE_SCALE_BASE) / RATE_SCALE_BASE

// Performance Fee
performanceFeeShares = (sharePrice - highWaterMark) * sharesSupply * feeRate / (GAV - rawValueDue)
```

### Критические проверки:
```solidity
// Защита от переполнения
require(investmentAmount <= type(uint256).max / SHARES_UNIT);

// Минимальное количество shares
require(sharesReceived >= minSharesQuantity);

// Slippage protection
require(actualReceived >= expectedReceived * (10000 - maxSlippage) / 10000);
```

---

## РЕКОМЕНДАЦИИ ПО АУДИТУ ДЕНЕЖНЫХ ПОТОКОВ

### 1. Приоритетные проверки:
- [ ] Все математические операции на overflow/underflow
- [ ] Reentrancy protection во всех state-changing функциях
- [ ] Slippage protection в торговых операциях
- [ ] Oracle price validation и staleness checks
- [ ] Fee calculation accuracy и bounds checking

### 2. Тестовые сценарии:
- [ ] Extreme values (0, max uint256)
- [ ] Precision loss в математических операциях
- [ ] Race conditions в concurrent операциях
- [ ] Economic attacks через flash loans
- [ ] MEV extraction возможности

### 3. Мониторинг метрики:
- [ ] Total Value Locked (TVL) изменения
- [ ] Share price volatility
- [ ] Fee accumulation rates
- [ ] Slippage в торговых операциях
- [ ] External position performance

Этот анализ денежных потоков обеспечивает полное понимание экономических рисков и возможностей для атак в протоколе Enzyme Finance.
