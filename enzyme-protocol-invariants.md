# Enzyme Finance Protocol Invariants Analysis

## Overview

This document provides a comprehensive analysis of invariants in the Enzyme Finance protocol, categorized as either "white box" (implementation-specific) or "black box" (design-based) invariants. These invariants are critical properties that must be maintained for the protocol to function correctly and securely.

---

## BLACK BOX INVARIANTS
*Based on protocol design and documentation, independent of implementation details*

### 1. Fund Value Conservation Invariant
- **Type:** Black Box
- **Description:** The total fund value (GAV) must equal the sum of all tracked assets value plus external positions value minus debt value
- **Components:** ComptrollerLib, VaultProxy, ValueInterpreter
- **Implementation:**
  ```solidity
  // In ComptrollerLib.calcGav()
  gav_ = IValueInterpreter(getValueInterpreter()).calcCanonicalAssetsTotalValue(
      assets, balances, getDenominationAsset()
  ) + externalPositionsValue;
  ```
- **Critical for:** Fund valuation accuracy, investor protection

### 2. Share Value Correspondence Invariant
- **Type:** Black Box  
- **Description:** Share value must equal GAV / totalSupply (except when totalSupply = 0)
- **Components:** ComptrollerLib, VaultProxy
- **Implementation:**
  ```solidity
  function __calcGrossShareValue(uint256 _gav, uint256 _sharesSupply, uint256 _denominationAssetUnit)
      returns (uint256 grossShareValue_) {
      if (_sharesSupply == 0) {
          return _denominationAssetUnit; // 1:1 for first deposit
      }
      return _gav * SHARES_UNIT / _sharesSupply;
  }
  ```
- **Critical for:** Fair pricing, preventing dilution attacks

### 3. Share Supply Conservation Invariant
- **Type:** Black Box
- **Description:** Share operations must preserve conservation: `newTotalSupply = oldTotalSupply ± sharesDelta`
- **Components:** VaultProxy (ERC20 mechanism)
- **Implementation:** Standard `__mint()` and `__burn()` functions in SharesTokenBase
- **Critical for:** Token accounting integrity

### 4. Protocol Fee Accrual Invariant
- **Type:** Black Box
- **Description:** Protocol fees must accrue proportionally to time and AUM
- **Components:** ProtocolFeeTracker, VaultProxy
- **Implementation:**
  ```solidity
  // Formula: sharesDue = supply * rate * time / year / 10000
  uint256 rawSharesDue = sharesSupply * getFeeBpsForVault(_vaultProxy) * _secondsDue / SECONDS_IN_YEAR / MAX_BPS;
  ```
- **Critical for:** Protocol sustainability, fair fee collection

---

## WHITE BOX INVARIANTS
*Based on internal implementation knowledge and code structure*

### 5. Single Active Controller Invariant
- **Type:** White Box
- **Description:** Each VaultProxy must have exactly one active ComptrollerProxy as accessor
- **Components:** VaultProxy, FundDeployer
- **Implementation:**
  ```solidity
  modifier onlyAccessor() {
      require(msg.sender == accessor, "Only the designated accessor can make this call");
      _;
  }
  ```
- **Critical for:** Access control, preventing unauthorized operations

### 6. Controller-Vault Mapping Invariant
- **Type:** White Box
- **Description:** Each ComptrollerProxy must be mapped to exactly one VaultProxy in FundDeployer
- **Components:** FundDeployer
- **Implementation:**
  ```solidity
  mapping(address => address) private comptrollerProxyToVaultProxy;
  // Set during creation and never changed
  ```
- **Critical for:** Fund lifecycle management

### 7. Shares Action Timelock Invariant
- **Type:** White Box
- **Description:** After buying shares, users cannot transfer or redeem them until sharesActionTimelock expires
- **Components:** ComptrollerLib
- **Implementation:**
  ```solidity
  function __assertSharesActionNotTimelocked(address _vaultProxy, address _account) private view {
      require(
          block.timestamp - getLastSharesBoughtTimestampForAccount(_account) >= getSharesActionTimelock(),
          "Shares action timelocked"
      );
  }
  ```
- **Critical for:** MEV protection, preventing arbitrage attacks

### 8. Self-Reference Protection Invariant
- **Type:** White Box
- **Description:** VaultProxy cannot perform operations on its own shares as an asset
- **Components:** VaultLib
- **Implementation:**
  ```solidity
  modifier notShares(address _asset) {
      require(_asset != address(this), "Cannot act on shares");
      _;
  }
  ```
- **Critical for:** Preventing circular references, accounting errors

### 9. Policy Validation Invariant
- **Type:** White Box
- **Description:** All fund operations must pass policy validation before execution
- **Components:** PolicyManager, ComptrollerLib
- **Implementation:**
  ```solidity
  function validatePolicies(address _comptrollerProxy, PolicyHook _hook, bytes calldata _validationData) {
      require(
          msg.sender == _comptrollerProxy || IComptroller(_comptrollerProxy).isExtension(msg.sender),
          "validatePolicies: Caller not allowed"
      );
      // Validate all policies for the given hook
  }
  ```
- **Critical for:** Compliance, risk management

### 10. Asset Tracking Invariant
- **Type:** White Box
- **Description:** Only tracked assets are included in GAV calculations, but VaultProxy may hold untracked assets
- **Components:** VaultLib, ComptrollerLib
- **Implementation:**
  ```solidity
  // In calcGav() only getTrackedAssets() are used
  address[] memory assets = IVault(vaultProxyAddress).getTrackedAssets();
  ```
- **Critical for:** Accurate valuation, gas optimization

### 11. Position Limit Invariant
- **Type:** White Box
- **Description:** Total tracked assets + external positions must not exceed POSITIONS_LIMIT
- **Components:** VaultLib
- **Implementation:**
  ```solidity
  uint256 private constant POSITIONS_LIMIT; // Set in constructor
  // Checked when adding new positions
  ```
- **Critical for:** Gas limit protection, performance optimization

### 12. Reentrancy Protection Invariant
- **Type:** White Box
- **Description:** Critical functions must be protected against reentrant calls
- **Components:** ComptrollerLib (locksReentrance modifier)
- **Implementation:**
  ```solidity
  modifier locksReentrance() {
      require(!reentranceLocked, "Re-entrance");
      reentranceLocked = true;
      _;
      reentranceLocked = false;
  }
  ```
- **Critical for:** Security, preventing reentrancy attacks

### 13. Protocol Fee Payment Frequency Invariant
- **Type:** White Box
- **Description:** Protocol fee can only be paid once per block per fund
- **Components:** ProtocolFeeTracker
- **Implementation:**
  ```solidity
  uint256 lastPaid = getLastPaidForVault(vaultProxy);
  if (lastPaid >= block.timestamp) {
      return 0; // Already paid in this block
  }
  ```
- **Critical for:** Preventing double payments, gas optimization

### 14. Minimum Shares Supply Invariant
- **Type:** White Box
- **Description:** Some funds must maintain minimum shares supply in circulation
- **Components:** MinSharesSupplyFee
- **Implementation:**
  ```solidity
  uint256 private constant MIN_SHARES_SUPPLY = 1e6;
  // Shares locked at address(1)
  ```
- **Critical for:** Preventing fund closure attacks, maintaining liquidity

### 15. Fund State Invariant
- **Type:** White Box
- **Description:** ComptrollerProxy can only be in one state: uninitialized, initialized, active, or deactivated
- **Components:** ComptrollerLib
- **Implementation:** State checks in `init()`, `activate()`, `deactivate()` functions
- **Critical for:** Lifecycle management, preventing invalid operations

---

## SECURITY-CRITICAL INVARIANTS

### 16. Extension Authorization Invariant
- **Type:** White Box
- **Description:** Only registered extensions can call permissionedVaultAction
- **Components:** ComptrollerLib
- **Implementation:**
  ```solidity
  require(msg.sender == FEE_MANAGER || isExtension(msg.sender), "Unauthorized caller");
  ```
- **Critical for:** Access control, preventing unauthorized vault operations

### 17. Fund Ownership Invariant
- **Type:** White Box
- **Description:** Only fund owner can modify critical fund settings
- **Components:** VaultLib, PolicyManager
- **Implementation:**
  ```solidity
  modifier onlyOwner() {
      require(__msgSender() == owner, "Only the owner can call this function");
      _;
  }
  ```
- **Critical for:** Governance, preventing unauthorized configuration changes

---

## Audit Recommendations

When auditing the Enzyme Finance protocol, verify that:

1. **All invariants hold** under normal and edge case conditions
2. **State transitions** preserve invariant properties
3. **External integrations** don't violate internal invariants
4. **Upgrade mechanisms** maintain invariant consistency
5. **Emergency procedures** respect critical invariants

These invariants form the foundation of the protocol's security and correctness. Any violation could lead to fund loss, incorrect valuations, or protocol dysfunction.
